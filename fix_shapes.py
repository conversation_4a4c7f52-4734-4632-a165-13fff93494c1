#!/usr/bin/env python3
"""
修复形状不正确的文件
"""

import os
import numpy as np
from pathlib import Path
import cv2
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_file_shapes():
    """修复所有形状不正确的文件"""
    base_dir = "/DATA/FAST"
    fixed_count = 0
    error_count = 0
    
    for modality in ['FPP', 'TPP']:
        for split in ['train', 'test', 'validation']:
            dir_path = os.path.join(base_dir, modality, split)
            
            for file_path in Path(dir_path).glob("*.npy"):
                try:
                    data = np.load(file_path)
                    
                    if data.shape != (64, 64, 1):
                        logger.info(f"修复文件 {file_path}: {data.shape} -> (64, 64, 1)")
                        
                        # 如果是2D数据，添加通道维度
                        if len(data.shape) == 2:
                            if data.shape != (64, 64):
                                # 调整尺寸到64x64
                                data_resized = cv2.resize(data.astype(np.float32), (64, 64), 
                                                        interpolation=cv2.INTER_LINEAR)
                            else:
                                data_resized = data
                            
                            # 添加通道维度
                            data_fixed = np.expand_dims(data_resized, axis=2)
                        
                        # 如果是3D数据但尺寸不对
                        elif len(data.shape) == 3:
                            if data.shape[2] == 1:
                                # 移除通道维度，调整尺寸，再添加回来
                                data_2d = data[:, :, 0]
                                if data_2d.shape != (64, 64):
                                    data_resized = cv2.resize(data_2d.astype(np.float32), (64, 64), 
                                                            interpolation=cv2.INTER_LINEAR)
                                else:
                                    data_resized = data_2d
                                data_fixed = np.expand_dims(data_resized, axis=2)
                            else:
                                # 多通道数据，取第一个通道
                                data_2d = data[:, :, 0]
                                if data_2d.shape != (64, 64):
                                    data_resized = cv2.resize(data_2d.astype(np.float32), (64, 64), 
                                                            interpolation=cv2.INTER_LINEAR)
                                else:
                                    data_resized = data_2d
                                data_fixed = np.expand_dims(data_resized, axis=2)
                        
                        else:
                            logger.error(f"无法处理的数据形状 {file_path}: {data.shape}")
                            error_count += 1
                            continue
                        
                        # 确保数据类型和范围正确
                        data_fixed = data_fixed.astype(np.float32)
                        
                        # 重新归一化
                        data_min = np.min(data_fixed)
                        data_max = np.max(data_fixed)
                        if data_max > data_min:
                            data_fixed = (data_fixed - data_min) / (data_max - data_min)
                        
                        # 保存修复后的文件
                        np.save(file_path, data_fixed)
                        fixed_count += 1
                        
                except Exception as e:
                    logger.error(f"处理文件失败 {file_path}: {e}")
                    error_count += 1
    
    logger.info(f"修复完成: {fixed_count} 个文件修复, {error_count} 个错误")

def verify_all_shapes():
    """验证所有文件的形状"""
    base_dir = "/DATA/FAST"
    total_files = 0
    correct_files = 0
    error_files = 0
    
    for modality in ['FPP', 'TPP']:
        for split in ['train', 'test', 'validation']:
            dir_path = os.path.join(base_dir, modality, split)
            
            for file_path in Path(dir_path).glob("*.npy"):
                total_files += 1
                try:
                    data = np.load(file_path)
                    
                    if data.shape == (64, 64, 1):
                        correct_files += 1
                    else:
                        logger.error(f"形状错误 {file_path}: {data.shape}")
                        error_files += 1
                        
                    # 验证数值范围
                    if not (0 <= np.min(data) <= np.max(data) <= 1):
                        logger.warning(f"数值范围问题 {file_path}: [{np.min(data):.3f}, {np.max(data):.3f}]")
                        
                except Exception as e:
                    logger.error(f"读取文件失败 {file_path}: {e}")
                    error_files += 1
    
    logger.info(f"验证结果: {correct_files}/{total_files} 文件正确, {error_files} 个错误")
    return error_files == 0

if __name__ == "__main__":
    logger.info("开始修复形状问题...")
    fix_file_shapes()
    
    logger.info("验证修复结果...")
    success = verify_all_shapes()
    
    if success:
        logger.info("✓ 所有文件形状正确！")
    else:
        logger.error("✗ 仍有文件存在问题")
