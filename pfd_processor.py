#!/usr/bin/env python3
"""
FAST数据集处理脚本
从.pfd文件中提取FPP（频率相位图）和TPP（时间相位图）
"""

import os
import struct
import numpy as np
from pathlib import Path
import random
from typing import Tuple, List
import logging
from tqdm import tqdm
import cv2

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PFDProcessor:
    """处理.pfd文件的类"""
    
    def __init__(self):
        self.target_size = (64, 64)
        
    def read_pfd_file(self, filepath: str) -> Tuple[np.ndarray, dict]:
        """
        读取.pfd文件并返回profile数据和元数据
        基于PRESTO prepfold.py的实现
        """
        try:
            with open(filepath, 'rb') as f:
                # 读取前5个整数
                data = f.read(5*4)
                if len(data) < 20:
                    raise ValueError(f"文件太小: {filepath}")

                # 检测字节序
                testswap = struct.unpack('<iiiii', data)
                if max(abs(x) for x in testswap) > 100000:
                    swapchar = '>'  # 大端序
                    numdms, numperiods, numpdots, nsub, npart = struct.unpack('>iiiii', data)
                else:
                    swapchar = '<'  # 小端序
                    numdms, numperiods, numpdots, nsub, npart = testswap

                # 读取接下来的7个整数
                data = f.read(7*4)
                proflen, numchan, pstep, pdstep, dmstep, ndmfact, npfact = struct.unpack(swapchar+'iiiiiii', data)

                metadata = {
                    'numdms': numdms, 'numperiods': numperiods, 'numpdots': numpdots,
                    'nsub': nsub, 'npart': npart, 'proflen': proflen, 'numchan': numchan,
                    'swapchar': swapchar
                }

                logger.info(f"文件头部信息: npart={npart}, nsub={nsub}, proflen={proflen}")

                # 读取字符串长度和字符串（文件名）
                filenm_len = struct.unpack(swapchar+"i", f.read(4))[0]
                filenm = f.read(filenm_len)

                # 读取候选名长度和字符串
                candnm_len = struct.unpack(swapchar+"i", f.read(4))[0]
                candnm = f.read(candnm_len)

                # 读取望远镜名长度和字符串
                telescope_len = struct.unpack(swapchar+"i", f.read(4))[0]
                telescope = f.read(telescope_len)

                # 读取pgdev长度和字符串
                pgdev_len = struct.unpack(swapchar+"i", f.read(4))[0]
                pgdev = f.read(pgdev_len)

                # 跳过RA和DEC字符串（32字节）
                f.read(32)

                # 读取浮点数参数（2个double）
                dt, startT = struct.unpack(swapchar+"dd", f.read(2*8))

                # 读取更多浮点数参数（13个double）
                float_params = struct.unpack(swapchar+"d"*13, f.read(13*8))

                # 读取DM数组
                dms = struct.unpack(swapchar+"d"*numdms, f.read(numdms*8))

                # 读取period数组
                periods = struct.unpack(swapchar+"d"*numperiods, f.read(numperiods*8))

                # 读取pdot数组
                pdots = struct.unpack(swapchar+"d"*numpdots, f.read(numpdots*8))

                # 现在读取profile数据
                numprofs = nsub * npart
                logger.info(f"准备读取 {numprofs} 个profiles，每个长度 {proflen}")

                # 读取profiles
                profiles = np.zeros((npart, nsub, proflen), dtype='d')
                for ii in range(npart):
                    for jj in range(nsub):
                        profile_data = f.read(proflen * 8)  # 8字节per double
                        if len(profile_data) < proflen * 8:
                            raise ValueError(f"无法读取完整的profile数据 at [{ii},{jj}]")
                        profile = struct.unpack(swapchar + 'd' * proflen, profile_data)
                        profiles[ii, jj, :] = np.array(profile)

                logger.info(f"成功读取profiles，形状: {profiles.shape}")
                logger.info(f"数据范围: [{np.min(profiles):.3e}, {np.max(profiles):.3e}]")

                return profiles, metadata

        except Exception as e:
            logger.error(f"读取文件失败 {filepath}: {e}")
            raise
    
    def extract_fpp_tpp(self, profiles: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        从profiles中提取FPP和TPP图像
        
        Args:
            profiles: 3D数组 (npart, nsub, proflen)
            
        Returns:
            fpp: 频率相位图 (64, 64)
            tpp: 时间相位图 (64, 64)
        """
        # TPP: 在频率维度求和 (npart, proflen)
        tpp = np.sum(profiles, axis=1)
        
        # FPP: 在时间维度求和 (nsub, proflen)  
        fpp = np.sum(profiles, axis=0)
        
        # 调整尺寸到64x64
        tpp_resized = cv2.resize(tpp.astype(np.float32), self.target_size, interpolation=cv2.INTER_LINEAR)
        fpp_resized = cv2.resize(fpp.astype(np.float32), self.target_size, interpolation=cv2.INTER_LINEAR)
        
        return fpp_resized, tpp_resized
    
    def normalize_image(self, image: np.ndarray) -> np.ndarray:
        """
        将图像归一化到[0,1]范围
        
        Args:
            image: 输入图像
            
        Returns:
            normalized_image: 归一化后的图像
        """
        img_min = np.min(image)
        img_max = np.max(image)
        
        if img_max > img_min:
            normalized = (image - img_min) / (img_max - img_min)
        else:
            normalized = np.zeros_like(image)
            
        return normalized.astype(np.float32)
    
    def process_single_file(self, input_path: str, output_fpp_path: str, output_tpp_path: str) -> bool:
        """
        处理单个.pfd文件
        
        Args:
            input_path: 输入.pfd文件路径
            output_fpp_path: FPP输出路径
            output_tpp_path: TPP输出路径
            
        Returns:
            success: 是否成功处理
        """
        try:
            # 读取.pfd文件
            profiles, metadata = self.read_pfd_file(input_path)
            
            # 提取FPP和TPP
            fpp, tpp = self.extract_fpp_tpp(profiles)
            
            # 归一化
            fpp_norm = self.normalize_image(fpp)
            tpp_norm = self.normalize_image(tpp)
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_fpp_path), exist_ok=True)
            os.makedirs(os.path.dirname(output_tpp_path), exist_ok=True)
            
            # 保存为.npy文件
            np.save(output_fpp_path, fpp_norm)
            np.save(output_tpp_path, tpp_norm)
            
            return True
            
        except Exception as e:
            logger.error(f"处理文件失败 {input_path}: {e}")
            return False

def split_dataset(pulsar_files: List[str], rfi_files: List[str], 
                 train_pulsar: int, val_pulsar: int,
                 train_rfi: int, val_rfi: int) -> Tuple[dict, dict]:
    """
    划分数据集
    
    Args:
        pulsar_files: 脉冲星文件列表
        rfi_files: RFI文件列表
        train_pulsar: 训练集脉冲星数量
        val_pulsar: 验证集脉冲星数量
        train_rfi: 训练集RFI数量
        val_rfi: 验证集RFI数量
        
    Returns:
        pulsar_split: 脉冲星文件划分
        rfi_split: RFI文件划分
    """
    # 随机打乱
    random.shuffle(pulsar_files)
    random.shuffle(rfi_files)
    
    # 划分脉冲星文件
    pulsar_split = {
        'train': pulsar_files[:train_pulsar],
        'validation': pulsar_files[train_pulsar:train_pulsar+val_pulsar]
    }
    
    # 划分RFI文件
    rfi_split = {
        'train': rfi_files[:train_rfi],
        'validation': rfi_files[train_rfi:train_rfi+val_rfi]
    }
    
    return pulsar_split, rfi_split

def main():
    """主处理函数"""
    # 设置随机种子以确保可重现性
    random.seed(42)
    np.random.seed(42)

    processor = PFDProcessor()

    # 定义路径
    base_input_dir = "/DATA/PICS-ResNet_data"
    base_output_dir = "/DATA/FAST"

    # 创建输出目录结构
    for modality in ['FPP', 'TPP']:
        for split in ['train', 'test', 'validation']:
            for class_name in ['pulsar', 'rfi']:
                output_dir = os.path.join(base_output_dir, modality, split, class_name)
                os.makedirs(output_dir, exist_ok=True)

    logger.info("开始处理FAST数据集...")

    # 收集所有训练文件
    train_pulsar_files = list(Path(f"{base_input_dir}/train_data/pulsar").glob("*.pfd"))
    train_rfi_files = list(Path(f"{base_input_dir}/train_data/rfi").glob("*.pfd"))

    # 收集所有测试文件
    test_pulsar_files = list(Path(f"{base_input_dir}/test_data/pulsar").glob("*.pfd"))
    test_rfi_files = list(Path(f"{base_input_dir}/test_data/rfi").glob("*.pfd"))

    logger.info(f"找到训练文件: {len(train_pulsar_files)} 脉冲星, {len(train_rfi_files)} RFI")
    logger.info(f"找到测试文件: {len(test_pulsar_files)} 脉冲星, {len(test_rfi_files)} RFI")

    # 划分训练集和验证集
    pulsar_split, rfi_split = split_dataset(
        [str(f) for f in train_pulsar_files],
        [str(f) for f in train_rfi_files],
        train_pulsar=671, val_pulsar=166,
        train_rfi=797, val_rfi=201
    )

    # 处理统计
    total_files = 0
    success_count = 0
    error_files = []

    # 处理各个数据集
    datasets = {
        'train': {
            'pulsar': pulsar_split['train'],
            'rfi': rfi_split['train']
        },
        'validation': {
            'pulsar': pulsar_split['validation'],
            'rfi': rfi_split['validation']
        },
        'test': {
            'pulsar': [str(f) for f in test_pulsar_files],
            'rfi': [str(f) for f in test_rfi_files]
        }
    }

    for split_name, split_data in datasets.items():
        logger.info(f"处理 {split_name} 数据集...")

        for class_name, file_list in split_data.items():
            logger.info(f"处理 {split_name}/{class_name}: {len(file_list)} 个文件")

            for file_path in tqdm(file_list, desc=f"{split_name}/{class_name}"):
                total_files += 1

                # 生成输出文件名
                base_name = Path(file_path).stem
                fpp_output = os.path.join(base_output_dir, 'FPP', split_name, class_name, f"{base_name}.npy")
                tpp_output = os.path.join(base_output_dir, 'TPP', split_name, class_name, f"{base_name}.npy")

                # 处理文件
                if processor.process_single_file(file_path, fpp_output, tpp_output):
                    success_count += 1
                else:
                    error_files.append(file_path)

    # 生成处理报告
    logger.info("=" * 60)
    logger.info("处理完成！")
    logger.info(f"总文件数: {total_files}")
    logger.info(f"成功处理: {success_count}")
    logger.info(f"失败文件: {len(error_files)}")
    logger.info(f"成功率: {success_count/total_files*100:.2f}%")

    if error_files:
        logger.error("失败的文件:")
        for error_file in error_files[:10]:  # 只显示前10个
            logger.error(f"  {error_file}")
        if len(error_files) > 10:
            logger.error(f"  ... 还有 {len(error_files)-10} 个文件")

    # 验证输出
    logger.info("验证输出文件...")
    verify_output(base_output_dir)

def verify_output(base_output_dir: str):
    """验证输出文件的质量"""
    logger.info("开始质量验证...")

    expected_counts = {
        'train': {'pulsar': 671, 'rfi': 797},
        'validation': {'pulsar': 166, 'rfi': 201},
        'test': {'pulsar': 326, 'rfi': 13321}
    }

    for modality in ['FPP', 'TPP']:
        logger.info(f"验证 {modality} 数据...")

        for split_name, split_expected in expected_counts.items():
            for class_name, expected_count in split_expected.items():
                output_dir = os.path.join(base_output_dir, modality, split_name, class_name)
                actual_files = list(Path(output_dir).glob("*.npy"))
                actual_count = len(actual_files)

                logger.info(f"{modality}/{split_name}/{class_name}: {actual_count}/{expected_count} 文件")

                if actual_count != expected_count:
                    logger.warning(f"文件数量不匹配: 期望 {expected_count}, 实际 {actual_count}")

                # 检查几个文件的质量
                for i, file_path in enumerate(actual_files[:3]):
                    try:
                        data = np.load(file_path)
                        if data.shape != (64, 64):
                            logger.error(f"形状错误 {file_path}: {data.shape}")
                        if not (0 <= np.min(data) <= np.max(data) <= 1):
                            logger.error(f"数值范围错误 {file_path}: [{np.min(data):.3f}, {np.max(data):.3f}]")
                        if data.dtype not in [np.float32, np.float64]:
                            logger.error(f"数据类型错误 {file_path}: {data.dtype}")
                    except Exception as e:
                        logger.error(f"文件损坏 {file_path}: {e}")

if __name__ == "__main__":
    main()
