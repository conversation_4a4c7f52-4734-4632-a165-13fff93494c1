#!/usr/bin/env python3
"""
FAST数据集标准处理脚本
使用PRESTO标准API进行数据处理，确保学术标准和HTRU兼容性
"""

import os
import numpy as np
from pathlib import Path
import random
from typing import Tuple, List
import logging
from tqdm import tqdm
import cv2
from presto_compatible import pfd

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FASTPrestoProcessor:
    """FAST数据集的PRESTO标准处理器"""
    
    def __init__(self):
        self.target_size = (64, 64)
        
    def process_pfd_file(self, pfd_path: str) -> Tuple[np.ndarray, np.ndarray]:
        """
        使用PRESTO标准API处理单个.pfd文件
        
        Args:
            pfd_path: .pfd文件路径
            
        Returns:
            fpp: 频率相位图 (64, 64, 1)
            tpp: 时间相位图 (64, 64, 1)
        """
        try:
            # 使用PRESTO兼容的pfd类
            pfd_obj = pfd(pfd_path)
            
            # 执行色散延迟修正（PRESTO标准步骤）
            pfd_obj.dedisperse()
            
            # 生成时间相位图（TPP）
            tpp_data = pfd_obj.time_vs_phase(interp=False)  # (64, 64)
            
            # 生成频率相位图（FPP）
            fpp_data = pfd_obj.get_subbands_data()  # (128, 64)
            
            # 调整FPP尺寸到64x64
            fpp_resized = cv2.resize(fpp_data.astype(np.float32), self.target_size, 
                                   interpolation=cv2.INTER_LINEAR)
            
            # TPP已经是64x64，无需调整
            tpp_resized = tpp_data.astype(np.float32)
            
            # 归一化到[0,1]范围
            fpp_norm = self._normalize_image(fpp_resized)
            tpp_norm = self._normalize_image(tpp_resized)
            
            # 添加通道维度，确保输出为64x64x1
            fpp_final = np.expand_dims(fpp_norm, axis=2)  # (64, 64, 1)
            tpp_final = np.expand_dims(tpp_norm, axis=2)  # (64, 64, 1)
            
            # 验证垂直亮线特征
            self._verify_vertical_features(fpp_final, tpp_final, pfd_path)
            
            return fpp_final, tpp_final
            
        except Exception as e:
            logger.error(f"处理文件失败 {pfd_path}: {e}")
            raise
    
    def _normalize_image(self, image: np.ndarray) -> np.ndarray:
        """每个样本独立归一化到[0,1]范围"""
        img_min = np.min(image)
        img_max = np.max(image)
        
        if img_max > img_min:
            normalized = (image - img_min) / (img_max - img_min)
        else:
            normalized = np.zeros_like(image)
            
        return normalized.astype(np.float32)
    
    def _verify_vertical_features(self, fpp: np.ndarray, tpp: np.ndarray, filepath: str):
        """验证垂直亮线特征的正确提取"""
        # 检查FPP中的垂直特征（频率维度上的连续性）
        fpp_2d = fpp[:, :, 0]
        
        # 计算垂直方向的方差（应该较高，表示有垂直结构）
        vertical_var = np.var(fpp_2d, axis=0).mean()
        horizontal_var = np.var(fpp_2d, axis=1).mean()
        
        if vertical_var < horizontal_var * 0.5:
            logger.warning(f"可能的特征方向问题 {Path(filepath).name}: "
                         f"垂直方差={vertical_var:.3f}, 水平方差={horizontal_var:.3f}")
        else:
            logger.debug(f"垂直特征验证通过 {Path(filepath).name}")
    
    def split_dataset(self, pulsar_files: List[str], rfi_files: List[str]) -> dict:
        """
        按照指定数量划分数据集
        训练集：671脉冲星 + 797RFI = 1468
        验证集：166脉冲星 + 201RFI = 367  
        测试集：保持原有测试集不变
        """
        # 随机打乱训练文件
        random.shuffle(pulsar_files)
        random.shuffle(rfi_files)
        
        split_data = {
            'train': {
                'pulsar': pulsar_files[:671],
                'rfi': rfi_files[:797]
            },
            'validation': {
                'pulsar': pulsar_files[671:671+166],
                'rfi': rfi_files[797:797+201]
            }
        }
        
        logger.info(f"数据集划分完成:")
        logger.info(f"  训练集: {len(split_data['train']['pulsar'])}脉冲星 + {len(split_data['train']['rfi'])}RFI")
        logger.info(f"  验证集: {len(split_data['validation']['pulsar'])}脉冲星 + {len(split_data['validation']['rfi'])}RFI")
        
        return split_data
    
    def generate_filename(self, original_path: str, class_type: str, index: int) -> str:
        """
        生成标准化文件名
        脉冲星: pulsar_XXXX_positive.npy
        RFI: cand_XXXXXX_negative.npy
        """
        if class_type == 'pulsar':
            return f"pulsar_{index:04d}_positive.npy"
        else:
            return f"cand_{index:06d}_negative.npy"
    
    def process_dataset(self):
        """处理完整的FAST数据集"""
        logger.info("开始FAST数据集的PRESTO标准处理...")
        
        # 设置随机种子确保可重现性
        random.seed(42)
        np.random.seed(42)
        
        # 定义路径
        base_input_dir = "/DATA/PICS-ResNet_data"
        base_output_dir = "/DATA/FAST"
        
        # 创建简化的输出目录结构
        for modality in ['FPP', 'TPP']:
            for split in ['train', 'test', 'validation']:
                output_dir = os.path.join(base_output_dir, modality, split)
                os.makedirs(output_dir, exist_ok=True)
        
        # 收集训练文件
        train_pulsar_files = list(Path(f"{base_input_dir}/train_data/pulsar").glob("*.pfd"))
        train_rfi_files = list(Path(f"{base_input_dir}/train_data/rfi").glob("*.pfd"))
        
        # 收集测试文件
        test_pulsar_files = list(Path(f"{base_input_dir}/test_data/pulsar").glob("*.pfd"))
        test_rfi_files = list(Path(f"{base_input_dir}/test_data/rfi").glob("*.pfd"))
        
        logger.info(f"找到文件: 训练({len(train_pulsar_files)}脉冲星, {len(train_rfi_files)}RFI), "
                   f"测试({len(test_pulsar_files)}脉冲星, {len(test_rfi_files)}RFI)")
        
        # 划分训练集和验证集
        split_data = self.split_dataset([str(f) for f in train_pulsar_files],
                                       [str(f) for f in train_rfi_files])
        
        # 处理统计
        total_files = 0
        success_count = 0
        error_files = []
        
        # 处理各个数据集
        datasets = {
            'train': split_data['train'],
            'validation': split_data['validation'],
            'test': {
                'pulsar': [str(f) for f in test_pulsar_files],
                'rfi': [str(f) for f in test_rfi_files]
            }
        }
        
        for split_name, split_data in datasets.items():
            logger.info(f"处理 {split_name} 数据集...")
            
            for class_name, file_list in split_data.items():
                logger.info(f"处理 {split_name}/{class_name}: {len(file_list)} 个文件")
                
                for idx, file_path in enumerate(tqdm(file_list, desc=f"{split_name}/{class_name}")):
                    total_files += 1
                    
                    try:
                        # 使用PRESTO标准API处理文件
                        fpp, tpp = self.process_pfd_file(file_path)
                        
                        # 生成标准化文件名
                        if class_name == 'pulsar':
                            filename = self.generate_filename(file_path, 'pulsar', idx + 1)
                        else:
                            filename = self.generate_filename(file_path, 'rfi', idx + 1)
                        
                        # 保存FPP和TPP
                        fpp_path = os.path.join(base_output_dir, 'FPP', split_name, filename)
                        tpp_path = os.path.join(base_output_dir, 'TPP', split_name, filename)
                        
                        np.save(fpp_path, fpp)
                        np.save(tpp_path, tpp)
                        
                        success_count += 1
                        
                    except Exception as e:
                        logger.error(f"处理失败 {file_path}: {e}")
                        error_files.append(file_path)
        
        # 生成处理报告
        self._generate_report(total_files, success_count, error_files, base_output_dir)
    
    def _generate_report(self, total_files: int, success_count: int, 
                        error_files: List[str], output_dir: str):
        """生成详细的处理报告"""
        logger.info("=" * 60)
        logger.info("PRESTO标准处理完成！")
        logger.info(f"总文件数: {total_files}")
        logger.info(f"成功处理: {success_count}")
        logger.info(f"失败文件: {len(error_files)}")
        logger.info(f"成功率: {success_count/total_files*100:.2f}%")
        
        if error_files:
            logger.error("失败的文件:")
            for error_file in error_files[:10]:
                logger.error(f"  {error_file}")
        
        # 验证输出
        self._verify_output(output_dir)
    
    def _verify_output(self, base_output_dir: str):
        """验证输出文件的质量和HTRU兼容性"""
        logger.info("开始质量验证和HTRU兼容性检查...")
        
        expected_counts = {
            'train': 1468,      # 671脉冲星 + 797RFI
            'validation': 367,   # 166脉冲星 + 201RFI
            'test': 13647       # 326脉冲星 + 13321RFI
        }
        
        for modality in ['FPP', 'TPP']:
            logger.info(f"验证 {modality} 数据...")
            
            for split_name, expected_count in expected_counts.items():
                output_dir = os.path.join(base_output_dir, modality, split_name)
                actual_files = list(Path(output_dir).glob("*.npy"))
                actual_count = len(actual_files)
                
                logger.info(f"{modality}/{split_name}: {actual_count}/{expected_count} 文件")
                
                # 检查几个文件的质量
                for file_path in actual_files[:3]:
                    try:
                        data = np.load(file_path)
                        
                        # 验证形状为64x64x1
                        if data.shape != (64, 64, 1):
                            logger.error(f"形状错误 {file_path}: {data.shape}")
                        
                        # 验证数值范围[0,1]
                        if not (0 <= np.min(data) <= np.max(data) <= 1):
                            logger.error(f"数值范围错误 {file_path}: [{np.min(data):.3f}, {np.max(data):.3f}]")
                        
                        # 验证数据类型
                        if data.dtype != np.float32:
                            logger.error(f"数据类型错误 {file_path}: {data.dtype}")
                            
                    except Exception as e:
                        logger.error(f"文件验证失败 {file_path}: {e}")
        
        logger.info("质量验证完成！")

def main():
    """主函数"""
    processor = FASTPrestoProcessor()
    processor.process_dataset()

if __name__ == "__main__":
    main()
