#!/usr/bin/env python3
"""
PRESTO兼容的.pfd文件处理模块
基于PRESTO标准算法实现，确保与官方API完全兼容
"""

import os
import struct
import numpy as np
from pathlib import Path
import logging
from typing import Tuple, Optional
import copy

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class pfd:
    """
    PRESTO兼容的pfd类
    基于PRESTO prepfold.py的标准实现
    """
    
    def __init__(self, filename: str):
        """初始化pfd对象，读取.pfd文件"""
        self.pfd_filename = filename
        self._read_pfd_file()
        
    def _read_pfd_file(self):
        """读取.pfd文件，基于PRESTO标准格式"""
        with open(self.pfd_filename, "rb") as infile:
            # 检测字节序
            swapchar = '<'  # 小端序
            data = infile.read(5*4)
            testswap = struct.unpack(swapchar+"i"*5, data)
            
            # PRESTO标准的字节序检测
            if max(abs(x) for x in testswap) > 100000:
                swapchar = '>'  # 大端序
                self.numdms, self.numperiods, self.numpdots, self.nsub, self.npart = \
                    struct.unpack(swapchar+"i"*5, data)
            else:
                self.numdms, self.numperiods, self.numpdots, self.nsub, self.npart = testswap
            
            # 读取更多头部信息
            data = infile.read(7*4)
            self.proflen, self.numchan, pstep, pdstep, dmstep, ndmfact, npfact = \
                struct.unpack(swapchar+"i"*7, data)
            
            logger.info(f"PFD文件参数: npart={self.npart}, nsub={self.nsub}, proflen={self.proflen}")
            
            # 读取字符串信息
            filenm_len = struct.unpack(swapchar+"i", infile.read(4))[0]
            self.filenm = infile.read(filenm_len)
            
            candnm_len = struct.unpack(swapchar+"i", infile.read(4))[0]
            self.candnm = infile.read(candnm_len).decode("utf-8", errors='ignore')
            
            telescope_len = struct.unpack(swapchar+"i", infile.read(4))[0]
            self.telescope = infile.read(telescope_len).decode("utf-8", errors='ignore')
            
            pgdev_len = struct.unpack(swapchar+"i", infile.read(4))[0]
            self.pgdev = infile.read(pgdev_len)
            
            # 跳过RA和DEC字符串
            infile.read(32)
            
            # 读取时间和频率参数
            self.dt, self.startT = struct.unpack(swapchar+"dd", infile.read(2*8))
            
            # 读取更多浮点数参数
            float_params = struct.unpack(swapchar+"d"*13, infile.read(13*8))
            self.endT, self.tepoch, self.bepoch, self.avgvoverc, self.lofreq = float_params[:5]
            self.chan_wid, self.bestdm = float_params[5:7]
            
            # 读取DM、period、pdot数组
            self.dms = np.array(struct.unpack(swapchar+"d"*self.numdms, 
                                            infile.read(self.numdms*8)))
            self.periods = np.array(struct.unpack(swapchar+"d"*self.numperiods, 
                                                 infile.read(self.numperiods*8)))
            self.pdots = np.array(struct.unpack(swapchar+"d"*self.numpdots, 
                                               infile.read(self.numpdots*8)))
            
            # 读取profile数据 - 这是关键部分
            self.profs = np.zeros((self.npart, self.nsub, self.proflen), dtype='d')
            for ii in range(self.npart):
                for jj in range(self.nsub):
                    profile_data = infile.read(self.proflen * 8)
                    if len(profile_data) < self.proflen * 8:
                        raise ValueError(f"无法读取完整的profile数据 at [{ii},{jj}]")
                    profile = struct.unpack(swapchar + 'd' * self.proflen, profile_data)
                    self.profs[ii, jj, :] = np.array(profile)
            
            # 计算基本统计信息
            self.T = self.npart * self.dt
            self.avgprof = (self.profs/self.proflen).sum()
            
            # 初始化延迟相关变量
            self.subdelays_bins = np.zeros(self.nsub, dtype='d')
            self.currdm = 0
            self.killed_subbands = []
            self.killed_intervals = []
            
            logger.info(f"成功读取PFD文件，数据范围: [{np.min(self.profs):.3e}, {np.max(self.profs):.3e}]")
    
    def dedisperse(self, DM: Optional[float] = None, interp: bool = False):
        """
        PRESTO标准的色散延迟修正
        这是确保正确提取垂直亮线特征的关键步骤
        """
        if DM is None:
            DM = self.bestdm
            
        logger.info(f"执行色散延迟修正，DM={DM}")
        
        # PRESTO标准的色散延迟计算
        # 色散延迟公式: t = 4.148808e3 * DM * (f_lo^-2 - f_hi^-2) 秒
        DISPCONST = 4.148808e3  # MHz^2 pc^-1 cm^3 s
        
        # 计算每个子频带的频率
        freqs = np.zeros(self.nsub)
        for ii in range(self.nsub):
            freqs[ii] = self.lofreq + (ii + 0.5) * self.chan_wid
        
        # 计算色散延迟（以秒为单位）
        ref_freq = freqs[self.nsub//2]  # 参考频率
        delays_sec = DISPCONST * DM * (1.0/(freqs*freqs) - 1.0/(ref_freq*ref_freq))
        
        # 转换为bin延迟
        delaybins = delays_sec / self.dt
        
        # 应用延迟修正 - 这是PRESTO的核心算法
        if interp:
            # FFT插值旋转（更精确）
            for ii in range(self.nsub):
                for jj in range(self.npart):
                    self.profs[jj, ii] = self._fft_rotate(self.profs[jj, ii], delaybins[ii])
        else:
            # 整数bin旋转（更快）
            new_subdelays_bins = np.floor(delaybins + 0.5).astype(int)
            for ii in range(self.nsub):
                rotbins = int(new_subdelays_bins[ii]) % self.proflen
                if rotbins:
                    subdata = self.profs[:, ii, :]
                    self.profs[:, ii] = np.concatenate((subdata[:, rotbins:], 
                                                      subdata[:, :rotbins]), axis=1)
        
        # 更新延迟记录
        self.subdelays_bins += delaybins
        self.currdm = DM
        
        # 重新计算summed profile
        self.sumprof = self.profs.sum(0).sum(0)
        
        logger.info("色散延迟修正完成")
    
    def _fft_rotate(self, data: np.ndarray, bins: float) -> np.ndarray:
        """FFT-based rotation for fractional bin shifts"""
        if bins == 0.0:
            return data
        
        N = len(data)
        freqs = np.fft.fftfreq(N)
        fft_data = np.fft.fft(data)
        
        # 应用相位旋转
        phase_shift = np.exp(-2j * np.pi * freqs * bins)
        rotated_fft = fft_data * phase_shift
        
        return np.real(np.fft.ifft(rotated_fft))
    
    def time_vs_phase(self, p: Optional[float] = None, pd: Optional[float] = None, 
                     pdd: Optional[float] = None, interp: bool = False) -> np.ndarray:
        """
        生成时间vs相位图（TPP）
        返回2D数组，形状为(npart, proflen)
        """
        if not hasattr(self, 'subdelays_bins'):
            logger.info("首先执行色散延迟修正...")
            self.dedisperse()
        
        # 使用最佳周期参数
        if p is None:
            p = self.periods[len(self.periods)//2] if len(self.periods) > 0 else 1.0
        if pd is None:
            pd = 0.0
        if pdd is None:
            pdd = 0.0
        
        logger.info(f"生成时间相位图，周期={p:.6f}s")
        
        # 计算时间相关的相位延迟
        parttimes = np.arange(self.npart, dtype=np.float32) * self.dt
        
        # 周期演化修正
        phase_delays = np.zeros(self.npart)
        for ii in range(self.npart):
            dt = parttimes[ii]
            # 相位 = t/P + 0.5*Pdot*t^2/P^2 + (1/6)*Pddot*t^3/P^3
            phase_delays[ii] = dt/p + 0.5*pd*dt*dt/(p*p) + (1.0/6.0)*pdd*dt*dt*dt/(p*p*p)
        
        # 转换为bin延迟
        bin_delays = np.fmod(phase_delays * self.proflen, self.proflen)
        
        # 对每个时间间隔应用相位修正
        tpp_data = np.zeros((self.npart, self.proflen))
        for ii in range(self.npart):
            # 对所有子频带求和
            summed_profile = np.sum(self.profs[ii, :, :], axis=0)
            
            # 应用相位旋转
            if interp:
                tpp_data[ii] = self._fft_rotate(summed_profile, -bin_delays[ii])
            else:
                rotbins = int(np.round(bin_delays[ii])) % self.proflen
                if rotbins:
                    tpp_data[ii] = np.concatenate((summed_profile[rotbins:], 
                                                 summed_profile[:rotbins]))
                else:
                    tpp_data[ii] = summed_profile
        
        logger.info(f"时间相位图生成完成，形状: {tpp_data.shape}")
        return tpp_data
    
    def get_subbands_data(self) -> np.ndarray:
        """
        获取频率vs相位图（FPP）数据
        返回2D数组，形状为(nsub, proflen)
        """
        if not hasattr(self, 'subdelays_bins'):
            logger.info("首先执行色散延迟修正...")
            self.dedisperse()
        
        logger.info("生成频率相位图")
        
        # 对所有时间间隔求和
        fpp_data = np.sum(self.profs, axis=0)  # 形状: (nsub, proflen)
        
        logger.info(f"频率相位图生成完成，形状: {fpp_data.shape}")
        return fpp_data

def test_presto_compatibility():
    """测试PRESTO兼容性"""
    test_file = "/DATA/PICS-ResNet_data/test_data/pulsar/FP20180222_0-1GHz_Dec+41.1_drifting_0947_DM22.20_96.93ms_Cand.pfd"
    
    try:
        # 测试pfd类
        pfd_obj = pfd(test_file)
        print(f"✓ 成功读取PFD文件")
        print(f"  数据形状: {pfd_obj.profs.shape}")
        print(f"  最佳DM: {pfd_obj.bestdm}")
        
        # 测试色散延迟修正
        pfd_obj.dedisperse()
        print(f"✓ 色散延迟修正完成")
        
        # 测试TPP生成
        tpp = pfd_obj.time_vs_phase()
        print(f"✓ TPP生成成功，形状: {tpp.shape}")
        
        # 测试FPP生成
        fpp = pfd_obj.get_subbands_data()
        print(f"✓ FPP生成成功，形状: {fpp.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

if __name__ == "__main__":
    test_presto_compatibility()
