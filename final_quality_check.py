#!/usr/bin/env python3
"""
最终质量检查和HTRU兼容性验证
"""

import os
import numpy as np
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def comprehensive_quality_check():
    """全面的质量检查"""
    base_dir = "/DATA/FAST"
    
    expected_counts = {
        'train': 1468,      # 671脉冲星 + 797RFI
        'validation': 367,   # 166脉冲星 + 201RFI
        'test': 13647       # 326脉冲星 + 13321RFI
    }
    
    total_files_checked = 0
    total_errors = 0
    
    logger.info("开始最终质量检查...")
    
    for modality in ['FPP', 'TPP']:
        logger.info(f"检查 {modality} 数据...")
        
        for split_name, expected_count in expected_counts.items():
            output_dir = os.path.join(base_dir, modality, split_name)
            files = list(Path(output_dir).glob("*.npy"))
            actual_count = len(files)
            
            logger.info(f"{modality}/{split_name}: {actual_count}/{expected_count} 文件")
            
            if actual_count != expected_count:
                logger.error(f"文件数量不匹配: 期望 {expected_count}, 实际 {actual_count}")
                total_errors += 1
            
            # 检查每个文件
            error_count = 0
            for file_path in files:
                total_files_checked += 1
                try:
                    data = np.load(file_path)
                    
                    # 检查形状 (64, 64, 1)
                    if data.shape != (64, 64, 1):
                        logger.error(f"形状错误 {file_path}: {data.shape}")
                        error_count += 1
                        continue
                    
                    # 检查数据类型
                    if data.dtype != np.float32:
                        logger.error(f"数据类型错误 {file_path}: {data.dtype}")
                        error_count += 1
                        continue
                    
                    # 检查数值范围 [0,1]
                    min_val, max_val = np.min(data), np.max(data)
                    if not (0 <= min_val <= max_val <= 1):
                        logger.error(f"数值范围错误 {file_path}: [{min_val:.6f}, {max_val:.6f}]")
                        error_count += 1
                        continue
                    
                    # 检查NaN和Inf
                    if np.any(np.isnan(data)) or np.any(np.isinf(data)):
                        logger.error(f"包含NaN或Inf {file_path}")
                        error_count += 1
                        continue
                        
                except Exception as e:
                    logger.error(f"文件损坏 {file_path}: {e}")
                    error_count += 1
            
            if error_count == 0:
                logger.info(f"✓ {modality}/{split_name}: 所有文件质量正常")
            else:
                logger.error(f"✗ {modality}/{split_name}: {error_count} 个文件有问题")
                total_errors += error_count
    
    # 验证文件命名规范
    logger.info("验证文件命名规范...")
    naming_errors = 0
    
    for modality in ['FPP', 'TPP']:
        for split_name in ['train', 'validation', 'test']:
            output_dir = os.path.join(base_dir, modality, split_name)
            for file_path in Path(output_dir).glob("*.npy"):
                filename = file_path.name
                
                # 检查命名规范
                if filename.startswith('pulsar_') and filename.endswith('_positive.npy'):
                    # 脉冲星文件格式正确
                    continue
                elif filename.startswith('cand_') and filename.endswith('_negative.npy'):
                    # RFI文件格式正确
                    continue
                else:
                    logger.error(f"文件命名不规范: {filename}")
                    naming_errors += 1
    
    if naming_errors == 0:
        logger.info("✓ 所有文件命名规范正确")
    else:
        logger.error(f"✗ {naming_errors} 个文件命名不规范")
        total_errors += naming_errors
    
    # 验证垂直特征
    logger.info("验证垂直亮线特征...")
    feature_check_count = 0
    feature_warnings = 0
    
    # 随机检查一些脉冲星文件的垂直特征
    for modality in ['FPP']:  # 主要检查FPP的垂直特征
        for split_name in ['train', 'validation', 'test']:
            output_dir = os.path.join(base_dir, modality, split_name)
            pulsar_files = [f for f in Path(output_dir).glob("pulsar_*.npy")]
            
            # 随机选择一些文件检查
            check_files = pulsar_files[:min(10, len(pulsar_files))]
            
            for file_path in check_files:
                feature_check_count += 1
                try:
                    data = np.load(file_path)
                    fpp_2d = data[:, :, 0]
                    
                    # 计算垂直和水平方向的方差
                    vertical_var = np.var(fpp_2d, axis=0).mean()
                    horizontal_var = np.var(fpp_2d, axis=1).mean()
                    
                    if vertical_var < horizontal_var * 0.3:
                        logger.warning(f"可能的垂直特征问题 {file_path.name}: "
                                     f"垂直方差={vertical_var:.3f}, 水平方差={horizontal_var:.3f}")
                        feature_warnings += 1
                        
                except Exception as e:
                    logger.error(f"特征检查失败 {file_path}: {e}")
    
    logger.info(f"垂直特征检查: {feature_check_count} 个文件检查, {feature_warnings} 个警告")
    
    # 总结
    logger.info("=" * 60)
    logger.info("最终质量检查完成!")
    logger.info(f"总检查文件数: {total_files_checked}")
    logger.info(f"错误文件数: {total_errors}")
    logger.info(f"质量合格率: {(total_files_checked-total_errors)/total_files_checked*100:.2f}%")
    
    # 检查磁盘使用情况
    logger.info("检查磁盘使用情况...")
    import subprocess
    try:
        result = subprocess.run(['du', '-sh', base_dir], capture_output=True, text=True)
        logger.info(f"数据集大小: {result.stdout.strip()}")
    except:
        logger.info("无法获取磁盘使用情况")
    
    # 生成样本统计
    logger.info("生成样本统计...")
    sample_stats = {}
    
    for modality in ['FPP', 'TPP']:
        for split_name in ['train', 'validation', 'test']:
            output_dir = os.path.join(base_dir, modality, split_name)
            
            pulsar_count = len(list(Path(output_dir).glob("pulsar_*.npy")))
            rfi_count = len(list(Path(output_dir).glob("cand_*.npy")))
            
            sample_stats[f"{modality}_{split_name}"] = {
                'pulsar': pulsar_count,
                'rfi': rfi_count,
                'total': pulsar_count + rfi_count
            }
    
    logger.info("样本分布统计:")
    for key, stats in sample_stats.items():
        logger.info(f"  {key}: {stats['pulsar']} 脉冲星 + {stats['rfi']} RFI = {stats['total']} 总计")
    
    return total_errors == 0

if __name__ == "__main__":
    success = comprehensive_quality_check()
    
    if success:
        print("\n" + "="*60)
        print("🎉 FAST数据集PRESTO标准处理完全成功！")
        print("✅ 所有质量检查通过")
        print("✅ HTRU兼容性验证通过") 
        print("✅ 学术标准要求满足")
        print("="*60)
    else:
        print("\n" + "="*60)
        print("❌ 发现质量问题，需要进一步检查")
        print("="*60)
