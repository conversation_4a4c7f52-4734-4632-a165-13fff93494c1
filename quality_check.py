#!/usr/bin/env python3
"""
FAST数据集质量检查脚本
"""

import os
import numpy as np
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def detailed_quality_check():
    """详细的质量检查"""
    base_dir = "/DATA/FAST"
    
    expected_counts = {
        'train': {'pulsar': 671, 'rfi': 797},
        'validation': {'pulsar': 166, 'rfi': 201},
        'test': {'pulsar': 326, 'rfi': 13321}
    }
    
    total_files_checked = 0
    total_errors = 0
    
    logger.info("开始详细质量检查...")
    
    for modality in ['FPP', 'TPP']:
        logger.info(f"检查 {modality} 数据...")
        
        for split_name, split_expected in expected_counts.items():
            for class_name, expected_count in split_expected.items():
                output_dir = os.path.join(base_dir, modality, split_name, class_name)
                files = list(Path(output_dir).glob("*.npy"))
                actual_count = len(files)
                
                logger.info(f"{modality}/{split_name}/{class_name}: {actual_count} 文件")
                
                if actual_count != expected_count:
                    logger.error(f"文件数量不匹配: 期望 {expected_count}, 实际 {actual_count}")
                    total_errors += 1
                
                # 检查每个文件
                error_count = 0
                for file_path in files:
                    total_files_checked += 1
                    try:
                        data = np.load(file_path)
                        
                        # 检查形状
                        if data.shape != (64, 64):
                            logger.error(f"形状错误 {file_path}: {data.shape}")
                            error_count += 1
                            continue
                        
                        # 检查数据类型
                        if data.dtype not in [np.float32, np.float64]:
                            logger.error(f"数据类型错误 {file_path}: {data.dtype}")
                            error_count += 1
                            continue
                        
                        # 检查数值范围
                        min_val, max_val = np.min(data), np.max(data)
                        if not (0 <= min_val <= max_val <= 1):
                            logger.error(f"数值范围错误 {file_path}: [{min_val:.6f}, {max_val:.6f}]")
                            error_count += 1
                            continue
                        
                        # 检查是否有NaN或Inf
                        if np.any(np.isnan(data)) or np.any(np.isinf(data)):
                            logger.error(f"包含NaN或Inf {file_path}")
                            error_count += 1
                            continue
                            
                    except Exception as e:
                        logger.error(f"文件损坏 {file_path}: {e}")
                        error_count += 1
                
                if error_count == 0:
                    logger.info(f"✓ {modality}/{split_name}/{class_name}: 所有文件质量正常")
                else:
                    logger.error(f"✗ {modality}/{split_name}/{class_name}: {error_count} 个文件有问题")
                    total_errors += error_count
    
    # 总结
    logger.info("=" * 60)
    logger.info("质量检查完成!")
    logger.info(f"总检查文件数: {total_files_checked}")
    logger.info(f"错误文件数: {total_errors}")
    logger.info(f"质量合格率: {(total_files_checked-total_errors)/total_files_checked*100:.2f}%")
    
    # 检查文件大小分布
    logger.info("检查文件大小分布...")
    file_sizes = []
    for modality in ['FPP', 'TPP']:
        for split_name in ['train', 'validation', 'test']:
            for class_name in ['pulsar', 'rfi']:
                output_dir = os.path.join(base_dir, modality, split_name, class_name)
                for file_path in Path(output_dir).glob("*.npy"):
                    file_sizes.append(os.path.getsize(file_path))
    
    if file_sizes:
        file_sizes = np.array(file_sizes)
        logger.info(f"文件大小统计 (字节):")
        logger.info(f"  最小: {np.min(file_sizes)}")
        logger.info(f"  最大: {np.max(file_sizes)}")
        logger.info(f"  平均: {np.mean(file_sizes):.0f}")
        logger.info(f"  中位数: {np.median(file_sizes):.0f}")
    
    # 随机抽样检查数据质量
    logger.info("随机抽样检查数据内容...")
    sample_files = []
    for modality in ['FPP', 'TPP']:
        for split_name in ['train', 'validation', 'test']:
            for class_name in ['pulsar', 'rfi']:
                output_dir = os.path.join(base_dir, modality, split_name, class_name)
                files = list(Path(output_dir).glob("*.npy"))
                if files:
                    sample_files.append(files[0])  # 取第一个文件作为样本
    
    for sample_file in sample_files[:6]:  # 检查6个样本
        try:
            data = np.load(sample_file)
            logger.info(f"样本 {sample_file.name}:")
            logger.info(f"  形状: {data.shape}")
            logger.info(f"  数据类型: {data.dtype}")
            logger.info(f"  数值范围: [{np.min(data):.6f}, {np.max(data):.6f}]")
            logger.info(f"  均值: {np.mean(data):.6f}")
            logger.info(f"  标准差: {np.std(data):.6f}")
        except Exception as e:
            logger.error(f"无法读取样本 {sample_file}: {e}")

if __name__ == "__main__":
    detailed_quality_check()
