Tests f/fdot interpolation in program test_apps.c.
Num data pts      = 131072
dt per bin (s)    = 7.62939453125e-06
Pulse shape       = Sine
Rounding format   = Whole Numbers
Pulse freq (hz)   = 2334.0216055
fdot (s-2)        = 23.456789
fdotdot (s-3)     = 0
Pulse amp         = 1
Pulse phs (deg)   = 0
DC backgrnd level = 0
Binary period (s) = 0
Bin asini/c (s)   = 0
Bin eccentricity  = 0
Ang of Peri (deg) = 0
Tm since peri (s) = 0
Amp Mod amplitude = 0
Amp Mod phs (deg) = 0
Amp Mod freq (hz) = 0
Noise type        = Other
Noise sigma       = 0
On/Off Pair  1    = 0 1
