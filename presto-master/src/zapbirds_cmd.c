/*****
  command line parser -- generated by clig
  (http://wsd.iitb.fhg.de/~kir/clighome/)

  The command line parser `clig':
  (C) 1995-2004 <PERSON> (<EMAIL>)
*****/

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <ctype.h>
#include <errno.h>
#include <limits.h>
#include <float.h>
#include <math.h>

#include "zapbirds_cmd.h"

char *Program;

/*@-null*/

static Cmdline cmd = {
  /***** -zap: Zap the birds in the FFT from 'zapfile' (write to the FFT file) */
    /* zapP = */ 0,
  /***** -zapfile: A file of freqs and widths to zap from the FFT (when using '-zap') */
    /* zapfileP = */ 0,
    /* zapfile = */ (char *) 0,
    /* zapfileC = */ 0,
  /***** -in: A file containing a list of freqs (Hz) and the # of harmonics to zap */
    /* inzapfileP = */ 0,
    /* inzapfile = */ (char *) 0,
    /* inzapfileC = */ 0,
  /***** -out: A file to write that will contain the freqs and widths (Hz) zapped */
    /* outzapfileP = */ 0,
    /* outzapfile = */ (char *) 0,
    /* outzapfileC = */ 0,
  /***** -baryv: The radial velocity component (v/c) towards the target during the obs */
    /* baryvP = */ 1,
    /* baryv = */ 0.0,
    /* baryvC = */ 1,
  /***** uninterpreted rest of command line */
    /* argc = */ 0,
    /* argv = */ (char **) 0,
  /***** the original command line concatenated */
    /* full_cmd_line = */ NULL
};

/*@=null*/

/***** let LCLint run more smoothly */
/*@-predboolothers*/
/*@-boolops*/


/******************************************************************/
/*****
 This is a bit tricky. We want to make a difference between overflow
 and underflow and we want to allow v==Inf or v==-Inf but not
 v>FLT_MAX. 

 We don't use fabs to avoid linkage with -lm.
*****/
static void checkFloatConversion(double v, char *option, char *arg)
{
    char *err = NULL;

    if ((errno == ERANGE && v != 0.0)   /* even double overflowed */
        ||(v < HUGE_VAL && v > -HUGE_VAL && (v < 0.0 ? -v : v) > (double) FLT_MAX)) {
        err = "large";
    } else if ((errno == ERANGE && v == 0.0)
               || (v != 0.0 && (v < 0.0 ? -v : v) < (double) FLT_MIN)) {
        err = "small";
    }
    if (err) {
        fprintf(stderr,
                "%s: parameter `%s' of option `%s' to %s to represent\n",
                Program, arg, option, err);
        exit(EXIT_FAILURE);
    }
}

int getIntOpt(int argc, char **argv, int i, int *value, int force)
{
    char *end;
    long v;

    if (++i >= argc)
        goto nothingFound;

    errno = 0;
    v = strtol(argv[i], &end, 0);

  /***** check for conversion error */
    if (end == argv[i])
        goto nothingFound;

  /***** check for surplus non-whitespace */
    while (isspace((int) *end))
        end += 1;
    if (*end)
        goto nothingFound;

  /***** check if it fits into an int */
    if (errno == ERANGE || v > (long) INT_MAX || v < (long) INT_MIN) {
        fprintf(stderr,
                "%s: parameter `%s' of option `%s' to large to represent\n",
                Program, argv[i], argv[i - 1]);
        exit(EXIT_FAILURE);
    }
    *value = (int) v;

    return i;

  nothingFound:
    if (!force)
        return i - 1;

    fprintf(stderr,
            "%s: missing or malformed integer value after option `%s'\n",
            Program, argv[i - 1]);
    exit(EXIT_FAILURE);
}

/**********************************************************************/

int getIntOpts(int argc, char **argv, int i, int **values, int cmin, int cmax)
/*****
  We want to find at least cmin values and at most cmax values.
  cmax==-1 then means infinitely many are allowed.
*****/
{
    int alloced, used;
    char *end;
    long v;
    if (i + cmin >= argc) {
        fprintf(stderr,
                "%s: option `%s' wants at least %d parameters\n",
                Program, argv[i], cmin);
        exit(EXIT_FAILURE);
    }

                                  /***** 
    alloc a bit more than cmin values. It does not hurt to have room
    for a bit more values than cmax.
  *****/
    alloced = cmin + 4;
    *values = (int *) calloc((size_t) alloced, sizeof(int));
    if (!*values) {
      outMem:
        fprintf(stderr,
                "%s: out of memory while parsing option `%s'\n", Program, argv[i]);
        exit(EXIT_FAILURE);
    }

    for (used = 0; (cmax == -1 || used < cmax) && used + i + 1 < argc; used++) {
        if (used == alloced) {
            alloced += 8;
            *values = (int *) realloc(*values, alloced * sizeof(int));
            if (!*values)
                goto outMem;
        }

        errno = 0;
        v = strtol(argv[used + i + 1], &end, 0);

    /***** check for conversion error */
        if (end == argv[used + i + 1])
            break;

    /***** check for surplus non-whitespace */
        while (isspace((int) *end))
            end += 1;
        if (*end)
            break;

    /***** check for overflow */
        if (errno == ERANGE || v > (long) INT_MAX || v < (long) INT_MIN) {
            fprintf(stderr,
                    "%s: parameter `%s' of option `%s' to large to represent\n",
                    Program, argv[i + used + 1], argv[i]);
            exit(EXIT_FAILURE);
        }

        (*values)[used] = (int) v;

    }

    if (used < cmin) {
        fprintf(stderr,
                "%s: parameter `%s' of `%s' should be an "
                "integer value\n", Program, argv[i + used + 1], argv[i]);
        exit(EXIT_FAILURE);
    }

    return i + used;
}

/**********************************************************************/

int getLongOpt(int argc, char **argv, int i, long *value, int force)
{
    char *end;

    if (++i >= argc)
        goto nothingFound;

    errno = 0;
    *value = strtol(argv[i], &end, 0);

  /***** check for conversion error */
    if (end == argv[i])
        goto nothingFound;

  /***** check for surplus non-whitespace */
    while (isspace((int) *end))
        end += 1;
    if (*end)
        goto nothingFound;

  /***** check for overflow */
    if (errno == ERANGE) {
        fprintf(stderr,
                "%s: parameter `%s' of option `%s' to large to represent\n",
                Program, argv[i], argv[i - 1]);
        exit(EXIT_FAILURE);
    }
    return i;

  nothingFound:
  /***** !force means: this parameter may be missing.*/
    if (!force)
        return i - 1;

    fprintf(stderr,
            "%s: missing or malformed value after option `%s'\n",
            Program, argv[i - 1]);
    exit(EXIT_FAILURE);
}

/**********************************************************************/

int getLongOpts(int argc, char **argv, int i, long **values, int cmin, int cmax)
/*****
  We want to find at least cmin values and at most cmax values.
  cmax==-1 then means infinitely many are allowed.
*****/
{
    int alloced, used;
    char *end;

    if (i + cmin >= argc) {
        fprintf(stderr,
                "%s: option `%s' wants at least %d parameters\n",
                Program, argv[i], cmin);
        exit(EXIT_FAILURE);
    }

                                  /***** 
    alloc a bit more than cmin values. It does not hurt to have room
    for a bit more values than cmax.
  *****/
    alloced = cmin + 4;
    *values = (long int *) calloc((size_t) alloced, sizeof(long));
    if (!*values) {
      outMem:
        fprintf(stderr,
                "%s: out of memory while parsing option `%s'\n", Program, argv[i]);
        exit(EXIT_FAILURE);
    }

    for (used = 0; (cmax == -1 || used < cmax) && used + i + 1 < argc; used++) {
        if (used == alloced) {
            alloced += 8;
            *values = (long int *) realloc(*values, alloced * sizeof(long));
            if (!*values)
                goto outMem;
        }

        errno = 0;
        (*values)[used] = strtol(argv[used + i + 1], &end, 0);

    /***** check for conversion error */
        if (end == argv[used + i + 1])
            break;

    /***** check for surplus non-whitespace */
        while (isspace((int) *end))
            end += 1;
        if (*end)
            break;

    /***** check for overflow */
        if (errno == ERANGE) {
            fprintf(stderr,
                    "%s: parameter `%s' of option `%s' to large to represent\n",
                    Program, argv[i + used + 1], argv[i]);
            exit(EXIT_FAILURE);
        }

    }

    if (used < cmin) {
        fprintf(stderr,
                "%s: parameter `%s' of `%s' should be an "
                "integer value\n", Program, argv[i + used + 1], argv[i]);
        exit(EXIT_FAILURE);
    }

    return i + used;
}

/**********************************************************************/

int getFloatOpt(int argc, char **argv, int i, float *value, int force)
{
    char *end;
    double v;

    if (++i >= argc)
        goto nothingFound;

    errno = 0;
    v = strtod(argv[i], &end);

  /***** check for conversion error */
    if (end == argv[i])
        goto nothingFound;

  /***** check for surplus non-whitespace */
    while (isspace((int) *end))
        end += 1;
    if (*end)
        goto nothingFound;

  /***** check for overflow */
    checkFloatConversion(v, argv[i - 1], argv[i]);

    *value = (float) v;

    return i;

  nothingFound:
    if (!force)
        return i - 1;

    fprintf(stderr,
            "%s: missing or malformed float value after option `%s'\n",
            Program, argv[i - 1]);
    exit(EXIT_FAILURE);

}

/**********************************************************************/

int getFloatOpts(int argc, char **argv, int i, float **values, int cmin, int cmax)
/*****
  We want to find at least cmin values and at most cmax values.
  cmax==-1 then means infinitely many are allowed.
*****/
{
    int alloced, used;
    char *end;
    double v;

    if (i + cmin >= argc) {
        fprintf(stderr,
                "%s: option `%s' wants at least %d parameters\n",
                Program, argv[i], cmin);
        exit(EXIT_FAILURE);
    }

                                  /***** 
    alloc a bit more than cmin values.
  *****/
    alloced = cmin + 4;
    *values = (float *) calloc((size_t) alloced, sizeof(float));
    if (!*values) {
      outMem:
        fprintf(stderr,
                "%s: out of memory while parsing option `%s'\n", Program, argv[i]);
        exit(EXIT_FAILURE);
    }

    for (used = 0; (cmax == -1 || used < cmax) && used + i + 1 < argc; used++) {
        if (used == alloced) {
            alloced += 8;
            *values = (float *) realloc(*values, alloced * sizeof(float));
            if (!*values)
                goto outMem;
        }

        errno = 0;
        v = strtod(argv[used + i + 1], &end);

    /***** check for conversion error */
        if (end == argv[used + i + 1])
            break;

    /***** check for surplus non-whitespace */
        while (isspace((int) *end))
            end += 1;
        if (*end)
            break;

    /***** check for overflow */
        checkFloatConversion(v, argv[i], argv[i + used + 1]);

        (*values)[used] = (float) v;
    }

    if (used < cmin) {
        fprintf(stderr,
                "%s: parameter `%s' of `%s' should be a "
                "floating-point value\n", Program, argv[i + used + 1], argv[i]);
        exit(EXIT_FAILURE);
    }

    return i + used;
}

/**********************************************************************/

int getDoubleOpt(int argc, char **argv, int i, double *value, int force)
{
    char *end;

    if (++i >= argc)
        goto nothingFound;

    errno = 0;
    *value = strtod(argv[i], &end);

  /***** check for conversion error */
    if (end == argv[i])
        goto nothingFound;

  /***** check for surplus non-whitespace */
    while (isspace((int) *end))
        end += 1;
    if (*end)
        goto nothingFound;

  /***** check for overflow */
    if (errno == ERANGE) {
        fprintf(stderr,
                "%s: parameter `%s' of option `%s' to %s to represent\n",
                Program, argv[i], argv[i - 1], (*value == 0.0 ? "small" : "large"));
        exit(EXIT_FAILURE);
    }

    return i;

  nothingFound:
    if (!force)
        return i - 1;

    fprintf(stderr,
            "%s: missing or malformed value after option `%s'\n",
            Program, argv[i - 1]);
    exit(EXIT_FAILURE);

}

/**********************************************************************/

int getDoubleOpts(int argc, char **argv, int i, double **values, int cmin, int cmax)
/*****
  We want to find at least cmin values and at most cmax values.
  cmax==-1 then means infinitely many are allowed.
*****/
{
    int alloced, used;
    char *end;

    if (i + cmin >= argc) {
        fprintf(stderr,
                "%s: option `%s' wants at least %d parameters\n",
                Program, argv[i], cmin);
        exit(EXIT_FAILURE);
    }

                                  /***** 
    alloc a bit more than cmin values.
  *****/
    alloced = cmin + 4;
    *values = (double *) calloc((size_t) alloced, sizeof(double));
    if (!*values) {
      outMem:
        fprintf(stderr,
                "%s: out of memory while parsing option `%s'\n", Program, argv[i]);
        exit(EXIT_FAILURE);
    }

    for (used = 0; (cmax == -1 || used < cmax) && used + i + 1 < argc; used++) {
        if (used == alloced) {
            alloced += 8;
            *values = (double *) realloc(*values, alloced * sizeof(double));
            if (!*values)
                goto outMem;
        }

        errno = 0;
        (*values)[used] = strtod(argv[used + i + 1], &end);

    /***** check for conversion error */
        if (end == argv[used + i + 1])
            break;

    /***** check for surplus non-whitespace */
        while (isspace((int) *end))
            end += 1;
        if (*end)
            break;

    /***** check for overflow */
        if (errno == ERANGE) {
            fprintf(stderr,
                    "%s: parameter `%s' of option `%s' to %s to represent\n",
                    Program, argv[i + used + 1], argv[i],
                    ((*values)[used] == 0.0 ? "small" : "large"));
            exit(EXIT_FAILURE);
        }

    }

    if (used < cmin) {
        fprintf(stderr,
                "%s: parameter `%s' of `%s' should be a "
                "double value\n", Program, argv[i + used + 1], argv[i]);
        exit(EXIT_FAILURE);
    }

    return i + used;
}

/**********************************************************************/

/**
  force will be set if we need at least one argument for the option.
*****/
int getStringOpt(int argc, char **argv, int i, char **value, int force)
{
    i += 1;
    if (i >= argc) {
        if (force) {
            fprintf(stderr, "%s: missing string after option `%s'\n",
                    Program, argv[i - 1]);
            exit(EXIT_FAILURE);
        }
        return i - 1;
    }

    if (!force && argv[i][0] == '-')
        return i - 1;
    *value = argv[i];
    return i;
}

/**********************************************************************/

int getStringOpts(int argc, char **argv, int i, char * **values, int cmin, int cmax)
/*****
  We want to find at least cmin values and at most cmax values.
  cmax==-1 then means infinitely many are allowed.
*****/
{
    int alloced, used;

    if (i + cmin >= argc) {
        fprintf(stderr,
                "%s: option `%s' wants at least %d parameters\n",
                Program, argv[i], cmin);
        exit(EXIT_FAILURE);
    }

    alloced = cmin + 4;

    *values = (char **) calloc((size_t) alloced, sizeof(char *));
    if (!*values) {
      outMem:
        fprintf(stderr,
                "%s: out of memory during parsing of option `%s'\n",
                Program, argv[i]);
        exit(EXIT_FAILURE);
    }

    for (used = 0; (cmax == -1 || used < cmax) && used + i + 1 < argc; used++) {
        if (used == alloced) {
            alloced += 8;
            *values = (char **) realloc(*values, alloced * sizeof(char *));
            if (!*values)
                goto outMem;
        }

        if (used >= cmin && argv[used + i + 1][0] == '-')
            break;
        (*values)[used] = argv[used + i + 1];
    }

    if (used < cmin) {
        fprintf(stderr,
                "%s: less than %d parameters for option `%s', only %d found\n",
                Program, cmin, argv[i], used);
        exit(EXIT_FAILURE);
    }

    return i + used;
}

/**********************************************************************/

void checkIntLower(char *opt, int *values, int count, int max)
{
    int i;

    for (i = 0; i < count; i++) {
        if (values[i] <= max)
            continue;
        fprintf(stderr,
                "%s: parameter %d of option `%s' greater than max=%d\n",
                Program, i + 1, opt, max);
        exit(EXIT_FAILURE);
    }
}

/**********************************************************************/

void checkIntHigher(char *opt, int *values, int count, int min)
{
    int i;

    for (i = 0; i < count; i++) {
        if (values[i] >= min)
            continue;
        fprintf(stderr,
                "%s: parameter %d of option `%s' smaller than min=%d\n",
                Program, i + 1, opt, min);
        exit(EXIT_FAILURE);
    }
}

/**********************************************************************/

void checkLongLower(char *opt, long *values, int count, long max)
{
    int i;

    for (i = 0; i < count; i++) {
        if (values[i] <= max)
            continue;
        fprintf(stderr,
                "%s: parameter %d of option `%s' greater than max=%ld\n",
                Program, i + 1, opt, max);
        exit(EXIT_FAILURE);
    }
}

/**********************************************************************/

void checkLongHigher(char *opt, long *values, int count, long min)
{
    int i;

    for (i = 0; i < count; i++) {
        if (values[i] >= min)
            continue;
        fprintf(stderr,
                "%s: parameter %d of option `%s' smaller than min=%ld\n",
                Program, i + 1, opt, min);
        exit(EXIT_FAILURE);
    }
}

/**********************************************************************/

void checkFloatLower(char *opt, float *values, int count, float max)
{
    int i;

    for (i = 0; i < count; i++) {
        if (values[i] <= max)
            continue;
        fprintf(stderr,
                "%s: parameter %d of option `%s' greater than max=%f\n",
                Program, i + 1, opt, max);
        exit(EXIT_FAILURE);
    }
}

/**********************************************************************/

void checkFloatHigher(char *opt, float *values, int count, float min)
{
    int i;

    for (i = 0; i < count; i++) {
        if (values[i] >= min)
            continue;
        fprintf(stderr,
                "%s: parameter %d of option `%s' smaller than min=%f\n",
                Program, i + 1, opt, min);
        exit(EXIT_FAILURE);
    }
}

/**********************************************************************/

void checkDoubleLower(char *opt, double *values, int count, double max)
{
    int i;

    for (i = 0; i < count; i++) {
        if (values[i] <= max)
            continue;
        fprintf(stderr,
                "%s: parameter %d of option `%s' greater than max=%f\n",
                Program, i + 1, opt, max);
        exit(EXIT_FAILURE);
    }
}

/**********************************************************************/

void checkDoubleHigher(char *opt, double *values, int count, double min)
{
    int i;

    for (i = 0; i < count; i++) {
        if (values[i] >= min)
            continue;
        fprintf(stderr,
                "%s: parameter %d of option `%s' smaller than min=%f\n",
                Program, i + 1, opt, min);
        exit(EXIT_FAILURE);
    }
}

/**********************************************************************/

static char *catArgv(int argc, char **argv)
{
    int i;
    size_t l;
    char *s, *t;

    for (i = 0, l = 0; i < argc; i++)
        l += (1 + strlen(argv[i]));
    s = (char *) malloc(l);
    if (!s) {
        fprintf(stderr, "%s: out of memory\n", Program);
        exit(EXIT_FAILURE);
    }
    strcpy(s, argv[0]);
    t = s;
    for (i = 1; i < argc; i++) {
        t = t + strlen(t);
        *t++ = ' ';
        strcpy(t, argv[i]);
    }
    return s;
}

/**********************************************************************/

void showOptionValues(void)
{
    int i;

    printf("Full command line is:\n`%s'\n", cmd.full_cmd_line);

  /***** -zap: Zap the birds in the FFT from 'zapfile' (write to the FFT file) */
    if (!cmd.zapP) {
        printf("-zap not found.\n");
    } else {
        printf("-zap found:\n");
    }

  /***** -zapfile: A file of freqs and widths to zap from the FFT (when using '-zap') */
    if (!cmd.zapfileP) {
        printf("-zapfile not found.\n");
    } else {
        printf("-zapfile found:\n");
        if (!cmd.zapfileC) {
            printf("  no values\n");
        } else {
            printf("  value = `%s'\n", cmd.zapfile);
        }
    }

  /***** -in: A file containing a list of freqs (Hz) and the # of harmonics to zap */
    if (!cmd.inzapfileP) {
        printf("-in not found.\n");
    } else {
        printf("-in found:\n");
        if (!cmd.inzapfileC) {
            printf("  no values\n");
        } else {
            printf("  value = `%s'\n", cmd.inzapfile);
        }
    }

  /***** -out: A file to write that will contain the freqs and widths (Hz) zapped */
    if (!cmd.outzapfileP) {
        printf("-out not found.\n");
    } else {
        printf("-out found:\n");
        if (!cmd.outzapfileC) {
            printf("  no values\n");
        } else {
            printf("  value = `%s'\n", cmd.outzapfile);
        }
    }

  /***** -baryv: The radial velocity component (v/c) towards the target during the obs */
    if (!cmd.baryvP) {
        printf("-baryv not found.\n");
    } else {
        printf("-baryv found:\n");
        if (!cmd.baryvC) {
            printf("  no values\n");
        } else {
            printf("  value = `%.40g'\n", cmd.baryv);
        }
    }
    if (!cmd.argc) {
        printf("no remaining parameters in argv\n");
    } else {
        printf("argv =");
        for (i = 0; i < cmd.argc; i++) {
            printf(" `%s'", cmd.argv[i]);
        }
        printf("\n");
    }
}

/**********************************************************************/

void usage(void)
{
    fprintf(stderr, "%s",
            "   [-zap] [-zapfile zapfile] [-in inzapfile] [-out outzapfile] [-baryv baryv] [--] infile ...\n");
    fprintf(stderr, "%s",
            "      Allows you to interactively or automatically zap interference from an FFT.\n");
    fprintf(stderr, "%s",
            "        -zap: Zap the birds in the FFT from 'zapfile' (write to the FFT file)\n");
    fprintf(stderr, "%s",
            "    -zapfile: A file of freqs and widths to zap from the FFT (when using '-zap')\n");
    fprintf(stderr, "%s", "              1 char* value\n");
    fprintf(stderr, "%s",
            "         -in: A file containing a list of freqs (Hz) and the # of harmonics to zap\n");
    fprintf(stderr, "%s", "              1 char* value\n");
    fprintf(stderr, "%s",
            "        -out: A file to write that will contain the freqs and widths (Hz) zapped\n");
    fprintf(stderr, "%s", "              1 char* value\n");
    fprintf(stderr, "%s",
            "      -baryv: The radial velocity component (v/c) towards the target during the obs\n");
    fprintf(stderr, "%s", "              1 double value between -0.1 and 0.1\n");
    fprintf(stderr, "%s", "              default: `0.0'\n");
    fprintf(stderr, "%s",
            "      infile: Input file name (no suffix) of floating point fft data.  A '.inf' file of the same name must also exist\n");
    fprintf(stderr, "%s", "              1...16384 values\n");
    fprintf(stderr, "%s", "  version: 09Jul20\n");
    fprintf(stderr, "%s", "  ");
    exit(EXIT_FAILURE);
}

/**********************************************************************/
Cmdline *parseCmdline(int argc, char **argv)
{
    int i;

    Program = argv[0];
    cmd.full_cmd_line = catArgv(argc, argv);
    for (i = 1, cmd.argc = 1; i < argc; i++) {
        if (0 == strcmp("--", argv[i])) {
            while (++i < argc)
                argv[cmd.argc++] = argv[i];
            continue;
        }

        if (0 == strcmp("-zap", argv[i])) {
            cmd.zapP = 1;
            continue;
        }

        if (0 == strcmp("-zapfile", argv[i])) {
            int keep = i;
            cmd.zapfileP = 1;
            i = getStringOpt(argc, argv, i, &cmd.zapfile, 1);
            cmd.zapfileC = i - keep;
            continue;
        }

        if (0 == strcmp("-in", argv[i])) {
            int keep = i;
            cmd.inzapfileP = 1;
            i = getStringOpt(argc, argv, i, &cmd.inzapfile, 1);
            cmd.inzapfileC = i - keep;
            continue;
        }

        if (0 == strcmp("-out", argv[i])) {
            int keep = i;
            cmd.outzapfileP = 1;
            i = getStringOpt(argc, argv, i, &cmd.outzapfile, 1);
            cmd.outzapfileC = i - keep;
            continue;
        }

        if (0 == strcmp("-baryv", argv[i])) {
            int keep = i;
            cmd.baryvP = 1;
            i = getDoubleOpt(argc, argv, i, &cmd.baryv, 1);
            cmd.baryvC = i - keep;
            checkDoubleLower("-baryv", &cmd.baryv, cmd.baryvC, 0.1);
            checkDoubleHigher("-baryv", &cmd.baryv, cmd.baryvC, -0.1);
            continue;
        }

        if (argv[i][0] == '-') {
            fprintf(stderr, "\n%s: unknown option `%s'\n\n", Program, argv[i]);
            usage();
        }
        argv[cmd.argc++] = argv[i];
    }                           /* for i */


    /*@-mustfree */
    cmd.argv = argv + 1;
    /*@=mustfree */
    cmd.argc -= 1;

    if (1 > cmd.argc) {
        fprintf(stderr, "%s: there should be at least 1 non-option argument(s)\n",
                Program);
        exit(EXIT_FAILURE);
    }
    if (16384 < cmd.argc) {
        fprintf(stderr, "%s: there should be at most 16384 non-option argument(s)\n",
                Program);
        exit(EXIT_FAILURE);
    }
    /*@-compmempass */ return &cmd;
}
