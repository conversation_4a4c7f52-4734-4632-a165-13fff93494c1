#ifndef __realfft_cmd__
#define __realfft_cmd__
/*****
  command line parser interface -- generated by clig 
  (http://wsd.iitb.fhg.de/~geg/clighome/)

  The command line parser `clig':
  (C) 1995-2004 <PERSON> (<EMAIL>)
*****/

typedef struct s_Cmdline {
  /***** -fwd: Force an forward FFT (sign=-1) to be performed */
  char forwardP;
  /***** -inv: Force an inverse FFT (sign=+1) to be performed */
  char inverseP;
  /***** -del: Delete the original file(s) when performing the FFT */
  char deleteP;
  /***** -disk: Force the use of the out-of-core memory FFT */
  char diskfftP;
  /***** -mem: Force the use of the in-core memory FFT */
  char memfftP;
  /***** -tmpdir: Scratch directory for temp file(s) in out-of-core FFT */
  char tmpdirP;
  char* tmpdir;
  int tmpdirC;
  /***** -outdir: Directory where result file(s) will reside */
  char outdirP;
  char* outdir;
  int outdirC;
  /***** uninterpreted command line parameters */
  int argc;
  /*@null*/char **argv;
  /***** the whole command line concatenated */
  char *full_cmd_line;
} Cmdline;


extern char *Program;
extern void usage(void);
extern /*@shared*/Cmdline *parseCmdline(int argc, char **argv);

extern void showOptionValues(void);

#endif

