#ifndef __toas2dat_cmd__
#define __toas2dat_cmd__
/*****
  command line parser interface -- generated by clig 
  (http://wsd.iitb.fhg.de/~geg/clighome/)

  The command line parser `clig':
  (C) 1995-2004 <PERSON> (<EMAIL>)
*****/

typedef struct s_Cmdline {
  /***** -n: The number of bins in the output time series */
  char numoutP;
  long numout;
  int numoutC;
  /***** -dt: Time interval in seconds for output time bins */
  char dtP;
  double dt;
  int dtC;
  /***** -t0: Time for the start of bin 0 (same units as the TOAs) */
  char t0P;
  double t0;
  int t0C;
  /***** -o: Name of the output time series file */
  char outfileP;
  char* outfile;
  int outfileC;
  /***** -text: TOAs are ASCII text (default is binary double) */
  char textP;
  /***** -float: TOAs are binary floats (default is binary double) */
  char floatP;
  /***** -sec: TOA unit is seconds (default is days) */
  char secP;
  /***** uninterpreted command line parameters */
  int argc;
  /*@null*/char **argv;
  /***** the whole command line concatenated */
  char *full_cmd_line;
} Cmdline;


extern char *Program;
extern void usage(void);
extern /*@shared*/Cmdline *parseCmdline(int argc, char **argv);

extern void showOptionValues(void);

#endif

