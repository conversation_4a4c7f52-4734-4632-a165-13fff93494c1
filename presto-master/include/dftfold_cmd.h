#ifndef __dftfold_cmd__
#define __dftfold_cmd__
/*****
  command line parser interface -- generated by clig 
  (http://wsd.iitb.fhg.de/~geg/clighome/)

  The command line parser `clig':
  (C) 1995-2004 <PERSON> (<EMAIL>)
*****/

typedef struct s_Cmdline {
  /***** -n: The number of DFT sub-vectors to save */
  char numvectP;
  int numvect;
  int numvectC;
  /***** -r: The Fourier frequency to fold (bins) */
  char rrP;
  double rr;
  int rrC;
  /***** -p: The period to fold (s) */
  char ppP;
  double pp;
  int ppC;
  /***** -f: The frequency to fold (Hz) */
  char ffP;
  double ff;
  int ffC;
  /***** -norm: Raw power divided by this normalizes the power */
  char normP;
  double norm;
  int normC;
  /***** -fftnorm: Use local powers from '.fft' file to get 'norm' */
  char fftnormP;
  /***** uninterpreted command line parameters */
  int argc;
  /*@null*/char **argv;
  /***** the whole command line concatenated */
  char *full_cmd_line;
} Cmdline;


extern char *Program;
extern void usage(void);
extern /*@shared*/Cmdline *parseCmdline(int argc, char **argv);

extern void showOptionValues(void);

#endif

