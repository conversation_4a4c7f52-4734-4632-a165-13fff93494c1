#ifndef __downsample_cmd__
#define __downsample_cmd__
/*****
  command line parser interface -- generated by clig 
  (http://wsd.iitb.fhg.de/~geg/clighome/)

  The command line parser `clig':
  (C) 1995-2004 <PERSON> (<EMAIL>)
*****/

typedef struct s_Cmdline {
  /***** -factor: The factor to downsample the data */
  char factorP;
  int factor;
  int factorC;
  /***** -o: Name of the output time series file (with suffix) */
  char outfileP;
  char* outfile;
  int outfileC;
  /***** uninterpreted command line parameters */
  int argc;
  /*@null*/char **argv;
  /***** the whole command line concatenated */
  char *full_cmd_line;
} Cmdline;


extern char *Program;
extern void usage(void);
extern /*@shared*/Cmdline *parseCmdline(int argc, char **argv);

extern void showOptionValues(void);

#endif

