#ifndef __readfile_cmd__
#define __readfile_cmd__
/*****
  command line parser interface -- generated by clig 
  (http://wsd.iitb.fhg.de/~geg/clighome/)

  The command line parser `clig':
  (C) 1995-2004 <PERSON> (<EMAIL>)
*****/

typedef struct s_Cmdline {
  /***** -page: Paginate the output like 'more' */
  char pageP;
  /***** -byte: Raw data in byte format */
  char bytP;
  /***** -b: Raw data in byte format */
  char sbytP;
  /***** -float: Raw data in floating point format */
  char fltP;
  /***** -f: Raw data in floating point format */
  char sfltP;
  /***** -double: Raw data in double precision format */
  char dblP;
  /***** -d: Raw data in double precision format */
  char sdblP;
  /***** -fcomplex: Raw data in float-complex format */
  char fcxP;
  /***** -fc: Raw data in float-complex format */
  char sfcxP;
  /***** -dcomplex: Raw data in double-complex format */
  char dcxP;
  /***** -dc: Raw data in double-complex format */
  char sdcxP;
  /***** -short: Raw data in short format */
  char shtP;
  /***** -s: Raw data in short format */
  char sshtP;
  /***** -int: Raw data in integer format */
  char igrP;
  /***** -i: Raw data in integer format */
  char sigrP;
  /***** -long: Raw data in long format */
  char lngP;
  /***** -l: Raw data in long format */
  char slngP;
  /***** -rzwcand: Raw data in rzw search candidate format */
  char rzwP;
  /***** -rzw: Raw data in rzw search candidate format */
  char srzwP;
  /***** -bincand: Raw data in bin search candidate format */
  char binP;
  /***** -bin: Raw data in bin search candidate format */
  char sbinP;
  /***** -position: Raw data in position struct format */
  char posP;
  /***** -pos: Raw data in position struct format */
  char sposP;
  /***** -pkmb: Raw data in Parkes Multibeam format */
  char pkmbP;
  /***** -bcpm: Raw data in BCPM format */
  char bcpmP;
  /***** -wapp: Raw data in WAPP format */
  char wappP;
  /***** -spigot: Raw data in Spigot Card format */
  char spigotP;
  /***** -filterbank: Raw data in SIGPROC filterbank format */
  char filterbankP;
  /***** -psrfits: Raw data in PSRFITS format */
  char psrfitsP;
  /***** -fortran: Raw data was written by a fortran program */
  char fortranP;
  /***** -index: The range of objects to display */
  char indexP;
  int *index;
  int indexC;
  /***** -nph: 0th FFT bin amplitude (for 'RZW' data) */
  char nphP;
  double nph;
  int nphC;
  /***** uninterpreted command line parameters */
  int argc;
  /*@null*/char **argv;
  /***** the whole command line concatenated */
  char *full_cmd_line;
} Cmdline;


extern char *Program;
extern void usage(void);
extern /*@shared*/Cmdline *parseCmdline(int argc, char **argv);

extern void showOptionValues(void);

#endif

