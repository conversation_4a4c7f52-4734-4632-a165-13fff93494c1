#ifndef __show_pfd_cmd__
#define __show_pfd_cmd__
/*****
  command line parser interface -- generated by clig 
  (http://wsd.iitb.fhg.de/~geg/clighome/)

  The command line parser `clig':
  (C) 1995-2004 <PERSON> (<EMAIL>)
*****/

typedef struct s_Cmdline {
  /***** -noxwin: Do not show the result plots on-screen, only make postscript files */
  char noxwinP;
  /***** -showfold: Use the input fold paramters (i.e. not the optimized values) when showing the plot */
  char showfoldP;
  /***** -scaleparts: Scale the part profiles independently */
  char scalepartsP;
  /***** -allgrey: Make all the images greyscale instead of color */
  char allgreyP;
  /***** -justprofs: Only output the profile portions of the plot */
  char justprofsP;
  /***** -portrait: Orient the output in portrait mode (for -justprofs) */
  char portraitP;
  /***** -events: The folded data were events instead of samples or bins */
  char eventsP;
  /***** -infoonly: Display the pfd info and exit without generating plots. */
  char infoonlyP;
  /***** -fixchi: Adjust the reduced chi^2 values so that off-pulse reduced chi^2 = 1 */
  char fixchiP;
  /***** -samples: Treat the data as samples and not as finite-duration integrated data */
  char samplesP;
  /***** -normalize: Normalize stats for each fold (i.e. to bandpass flatten subbands) */
  char normalizeP;
  /***** -killsubs: Comma separated string (no spaces!) of subbands to explicitly remove from analysis (i.e. zero out).  Ranges are specified by min:max[:step] */
  char killsubsstrP;
  char* killsubsstr;
  int killsubsstrC;
  /***** -killparts: Comma separated string (no spaces!) of intervals to explicitly remove from analysis (i.e. zero-out).  Ranges are specified by min:max[:step] */
  char killpartsstrP;
  char* killpartsstr;
  int killpartsstrC;
  /***** uninterpreted command line parameters */
  int argc;
  /*@null*/char **argv;
  /***** the whole command line concatenated */
  char *full_cmd_line;
} Cmdline;


extern char *Program;
extern void usage(void);
extern /*@shared*/Cmdline *parseCmdline(int argc, char **argv);

extern void showOptionValues(void);

#endif

