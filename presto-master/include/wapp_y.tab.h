/* A Bison parser, made by GNU Bison 2.3.  */

/* Skeleton interface for Bison's Yacc-like parsers in C

   Copyright (C) 1984, 1989, 1990, 2000, 2001, 2002, 2003, 2004, 2005, 2006
   Free Software Foundation, Inc.

   This program is free software; you can redistribute it and/or modify
   it under the terms of the GNU General Public License as published by
   the Free Software Foundation; either version 2, or (at your option)
   any later version.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License for more details.

   You should have received a copy of the GNU General Public License
   along with this program; if not, write to the Free Software
   Foundation, Inc., 51 Franklin Street, Fifth Floor,
   Boston, MA 02110-1301, USA.  */

/* As a special exception, you may create a larger work that contains
   part or all of the Bison parser skeleton and distribute that work
   under terms of your choice, so long as that work isn't itself a
   parser generator using the skeleton or a modified version thereof
   as a parser skeleton.  Alternatively, if you modify or redistribute
   the parser skeleton itself, you may (at your option) remove this
   special exception, which will cause the skeleton and the resulting
   Bison output files to be licensed under the GNU General Public
   License without this special exception.

   This special exception was added by the Free Software Foundation in
   version 2.2 of Bison.  */

/* Tokens.  */
#ifndef YYTOKENTYPE
# define YYTOKENTYPE
   /* Put the tokens into the symbol table, so that GDB and other debuggers
      know about them.  */
   enum yytokentype {
     STRUCT = 258,
     LBRACE = 259,
     RBRACE = 260,
     SEMI = 261,
     LB = 262,
     RB = 263,
     DOUBLE = 264,
     INTEGER = 265,
     LONG = 266,
     LONGLONG = 267,
     FLOAT = 268,
     SHORT = 269,
     UNSIGNED = 270,
     CHARSTAR = 271,
     BYTE = 272,
     VAR = 273,
     COMMENT = 274
   };
#endif
/* Tokens.  */
#define STRUCT 258
#define LBRACE 259
#define RBRACE 260
#define SEMI 261
#define LB 262
#define RB 263
#define DOUBLE 264
#define INTEGER 265
#define LONG 266
#define LONGLONG 267
#define FLOAT 268
#define SHORT 269
#define UNSIGNED 270
#define CHARSTAR 271
#define BYTE 272
#define VAR 273
#define COMMENT 274




#if ! defined YYSTYPE && ! defined YYSTYPE_IS_DECLARED
typedef union YYSTYPE
#line 14 "mkheader.y"
{
         char *var;
         int tok;
       }
/* Line 1489 of yacc.c.  */
#line 92 "wapp_y.tab.h"
	YYSTYPE;
# define yystype YYSTYPE /* obsolescent; will be withdrawn */
# define YYSTYPE_IS_DECLARED 1
# define YYSTYPE_IS_TRIVIAL 1
#endif

extern YYSTYPE yylval;

