# Admin data

Name bincand

Usage "Generates a set of fake orbits and their Fourier modulation spectra for a binary candidate (usually generated by search_bin).  It tries to return the optimum fit for the binary candidate."

Version [exec date +%d%b%y]

Commandline full_cmd_line

# Options (in order you want them to appear)

Double -plo    plo    {The low pulsar period to check (s)} \
	-r 0 oo
Double -phi    phi    {The high pulsar period to check (s)} \
	-r 0 oo
Double -rlo    rlo    {The low Fourier frequency bin to check} \
	-r 0 oo
Double -rhi    rhi    {The high Fourier frequency bin to check} \
	-r 0 oo
String -psr   psrname  {Name of pulsar to check for (do not include J or <PERSON>)}
Int -candnum  candnum  {Number of the candidate to optimize from candfile.} \
	-r 1 oo
String -candfile  candfile  {Name of the bincary candidate file.}

# Parameters for a binary pulsar

Flag -usr  usr    {Describe your own binary candidate.  Must include all of the following (assumed) parameters}
Double -pb   pb      {The orbital period (s)} \
	-r 0 oo
Double -x    asinic  {The projected orbital semi-major axis (lt-sec)} \
	-r 0 oo
Double -e    e       {The orbital eccentricity} \
	-r 0 0.9999999  -d 0
Double -To   To      {The time of periastron passage (MJD)} \
	-r 0 oo
Double -w    w       {Longitude of periastron (deg)} \
	-r 0 360
Double -wdot wdot    {Rate of advance of periastron (deg/yr)} \
	-r -oo oo  -d 0

Flag -mak  makefile  {Determine optimization parameters from 'infile.mak'}

# Rest of command line:

Rest infile {Input fft file name (without a suffix) of floating point fft data.  A '.inf' file of the same name must also exist} \
        -c 1 1




