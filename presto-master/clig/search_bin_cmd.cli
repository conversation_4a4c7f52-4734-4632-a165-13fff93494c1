# Admin data

Name search_bin

Usage "Searches a long FFT for binary pulsar candidates using a phase modulation search."

Version [exec date +%d%b%y]

Commandline full_cmd_line

# Options (in order you want them to appear)

Int -ncand   ncand   {Number of candidates to try to return} \
	-r 1 10000  -d 100
Int -minfft  minfft  {Power-of-2 length of the shortest miniFFT} \
	-r 8 1048576  -d 32
Int -maxfft  maxfft  {Power-of-2 length of the longest miniFFT} \
	-r 8 1048576  -d 65536
Float -flo     flo     {The low frequency (Hz) to check} \
	-r 0 oo   -d 1.0
Float -fhi     fhi     {The high frequency (Hz) to check} \
	-r 0 oo   -d 2000.0
Int -rlo     rlo     {The low Fourier frequency to check} \
	-r 0 oo
Int -rhi     rhi     {The high Fourier frequency to check} \
	-r 0 oo
Int -lobin   lobin   {The first Fourier frequency in the data file} \
	-r 0 oo  -d 0
Double -overlap overlap {Fraction of a short FFT length to shift before performing another} \
	-r 0.05 1.0  -d 0.25
Int -harmsum harmsum {Number of harmonics to sum in the miniFFTs} \
	-r 1 20  -d 3
Int -numbetween numbetween {Number of points to interpolate per Fourier bin (2 gives the usual bin value and an interbin)} \
	-r 1 16  -d 2
Int -stack   stack   {Number of stacked power spectra making up the data.  (The default means the data are complex amplitudes)} \
	-r 0 oo  -d 0
Flag -interbin interbin {Use interbinning instead of full-blown Fourier interpolation.  (Faster but less accurate and sensitive)}
Flag -noalias noalias {Do not add aliased powers to the harmonic sum.  (Faster but less accurate and sensitive)}

# Rest of command line:

Rest infile {Input file name (no suffix) of floating point fft data.  A '.inf' file of the same name must also exist} \
        -c 1 1




