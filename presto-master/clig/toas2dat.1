.\" clig manual page template
.\" (C) 1995 <PERSON> (<EMAIL>)
.\"
.\" This file was generated by
.\" clig -- command line interface generator
.\"
.\"
.\" Clig will always edit the lines between pairs of `cligPart ...',
.\" but will not complain, if a pair is missing. So, if you want to
.\" make up a certain part of the manual page by hand rather than have
.\" it edited by clig, remove the respective pair of cligPart-lines.
.\"
.\" cligPart TITLE
.TH "toas2dat" 1 "26Sep17" "Clig-manuals" "Programmer's Manual"
.\" cligPart TITLE end

.\" cligPart NAME
.SH NAME
toas2dat \- Converts TOAs into a binned time series.
.\" cligPart NAME end

.\" cligPart SYNOPSIS
.SH SYNOPSIS
.B toas2dat
-n numout
-dt dt
[-t0 t0]
-o outfile
[-text]
[-float]
[-sec]
file
.\" cligPart SYNOPSIS end

.\" cligPart OPTIONS
.SH OPTIONS
.IP -n
The number of bins in the output time series,
.br
1 Long value between 0 and oo.
.IP -dt
Time interval in seconds for output time bins,
.br
1 Double value between 0 and oo.
.IP -t0
Time for the start of bin 0 (same units as the TOAs),
.br
1 Double value.
.IP -o
Name of the output time series file,
.br
1 String value
.IP -text
TOAs are ASCII text (default is binary double).
.IP -float
TOAs are binary floats (default is binary double).
.IP -sec
TOA unit is seconds (default is days).
.IP file
Input TOA file name.
.\" cligPart OPTIONS end

.\" cligPart DESCRIPTION
.SH DESCRIPTION
This manual page was generated automagically by clig, the
Command Line Interface Generator. Actually the programmer
using clig was supposed to edit this part of the manual
page after
generating it with clig, but obviously (s)he didn't.

Sadly enough clig does not yet have the power to pick a good
program description out of blue air ;-(
.\" cligPart DESCRIPTION end
