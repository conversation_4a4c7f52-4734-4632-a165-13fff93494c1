# Admin data

Name readfile

Usage "Reads raw data from a binary file and displays it on stdout."

Version [exec date +%d%b%y]

Commandline full_cmd_line

# Options (in order you want them to appear)

Flag   	-page		page	{Paginate the output like 'more'}
Flag   	-byte		byt	{Raw data in byte format}
Flag   	-b		sbyt	{Raw data in byte format}
Flag   	-float		flt	{Raw data in floating point format}
Flag   	-f		sflt	{Raw data in floating point format}
Flag   	-double    	dbl 	{Raw data in double precision format}
Flag   	-d	    	sdbl 	{Raw data in double precision format}
Flag   	-fcomplex    	fcx	{Raw data in float-complex format}
Flag   	-fc	    	sfcx	{Raw data in float-complex format}
Flag   	-dcomplex    	dcx	{Raw data in double-complex format}
Flag   	-dc	    	sdcx	{Raw data in double-complex format}
Flag   	-short    	sht    	{Raw data in short format}
Flag   	-s    		ssht    {Raw data in short format}
Flag   	-int    	igr    	{Raw data in integer format}
Flag   	-i    		sigr    {Raw data in integer format}
Flag   	-long    	lng	{Raw data in long format}
Flag   	-l	    	slng	{Raw data in long format}
Flag   	-rzwcand    	rzw	{Raw data in rzw search candidate format}
Flag   	-rzw	    	srzw	{Raw data in rzw search candidate format}
Flag   	-bincand    	bin	{Raw data in bin search candidate format}
Flag   	-bin	    	sbin	{Raw data in bin search candidate format}
Flag   	-position    	pos	{Raw data in position struct format}
Flag   	-pos	    	spos	{Raw data in position struct format}
Flag   	-pkmb    	pkmb	{Raw data in Parkes Multibeam format}
Flag   	-bcpm    	bcpm	{Raw data in BCPM format}
Flag    -wapp           wapp    {Raw data in WAPP format}
Flag    -spigot         spigot  {Raw data in Spigot Card format}
Flag    -filterbank     filterbank  {Raw data in SIGPROC filterbank format}
Flag    -psrfits     psrfits  {Raw data in PSRFITS format}
Flag	-fortran        fortran {Raw data was written by a fortran program}
Int 	-index 		index 	{The range of objects to display} \
				-r -1 oo  -d 0 " -1"  -c 0 2
Double 	-nph		nph	{0th FFT bin amplitude (for 'RZW' data)} \
				-r -oo oo  -d 1.0

# Rest of command line:

Rest 			file 	{Input data file name.} \
				-c  1 1




