# Admin data

Name show_pfd

Usage "Displays or regenerates the Postscript for a 'pfd' file created by prepfold."

Version [exec date +%d%b%y]

Commandline full_cmd_line

# Options (in order you want them to appear)

Flag -noxwin     noxwin     {Do not show the result plots on-screen, only make postscript files}
Flag -showfold   showfold   {Use the input fold paramters (i.e. not the optimized values) when showing the plot}
Flag -scaleparts scaleparts {Scale the part profiles independently}
Flag -allgrey    allgrey    {Make all the images greyscale instead of color}
Flag -justprofs  justprofs  {Only output the profile portions of the plot}
Flag -portrait   portrait   {Orient the output in portrait mode (for -justprofs)}
Flag -events     events     {The folded data were events instead of samples or bins}
Flag -infoonly   infoonly   {Display the pfd info and exit without generating plots.}
Flag -fixchi     fixchi     {Adjust the reduced chi^2 values so that off-pulse reduced chi^2 = 1}
Flag -samples    samples    {Treat the data as samples and not as finite-duration integrated data}
Flag -normalize  normalize  {Normalize stats for each fold (i.e. to bandpass flatten subbands)}
String -killsubs killsubsstr {Comma separated string (no spaces!) of subbands to explicitly remove from analysis (i.e. zero out).  Ranges are specified by min:max[:step]}
String -killparts killpartsstr {Comma separated string (no spaces!) of intervals to explicitly remove from analysis (i.e. zero-out).  Ranges are specified by min:max[:step]}

# Rest of command line:

Rest infile {The input 'pfd' file name.} \
        -c 1 100




