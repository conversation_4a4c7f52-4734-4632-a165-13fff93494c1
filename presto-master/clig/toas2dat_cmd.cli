# Admin data

Name toas2dat

Usage "Converts TOAs into a binned time series."

Version [exec date +%d%b%y]

Commandline full_cmd_line

# Options (in order you want them to appear)

Long 	-n 	numout 	{The number of bins in the output time series} \
			-r 0 oo  -m
Double 	-dt	dt	{Time interval in seconds for output time bins} \
			-r 0 oo  -m
Double 	-t0	t0	{Time for the start of bin 0 (same units as the TOAs)}
String	-o	outfile {Name of the output time series file} \
			-m
Flag   	-text	text	{TOAs are ASCII text (default is binary double)}
Flag   	-float	float	{TOAs are binary floats (default is binary double)}
Flag	-sec	sec	{TOA unit is seconds (default is days)}

# Rest of command line:

Rest 		file 	{Input TOA file name} \
			-c 1 1




