.\" clig manual page template
.\" (C) 1995 <PERSON> (<EMAIL>)
.\"
.\" This file was generated by
.\" clig -- command line interface generator
.\"
.\"
.\" Clig will always edit the lines between pairs of `cligPart ...',
.\" but will not complain, if a pair is missing. So, if you want to
.\" make up a certain part of the manual page by hand rather than have
.\" it edited by clig, remove the respective pair of cligPart-lines.
.\"
.\" cligPart TITLE
.TH "dftfold" 1 "12Mar10" "Clig-manuals" "Programmer's Manual"
.\" cligPart TITLE end

.\" cligPart NAME
.SH NAME
dftfold \- Calculates the complex vector addition of a DFT frequency.
.\" cligPart NAME end

.\" cligPart SYNOPSIS
.SH SYNOPSIS
.B dftfold
[-n numvect]
[-r rr]
[-p pp]
[-f ff]
[-norm norm]
[-fftnorm]
infile
.\" cligPart SYNOPSIS end

.\" cligPart OPTIONS
.SH OPTIONS
.IP -n
The number of DFT sub-vectors to save,
.br
1 Int value between 2 and 262144.
.br
Default: `1000'
.IP -r
The Fourier frequency to fold (bins),
.br
1 Double value between 1.0 and 2000000000.0.
.IP -p
The period to fold (s),
.br
1 Double value between 0.00000001 and 100000.0.
.IP -f
The frequency to fold (Hz),
.br
1 Double value between 0.00000001 and 100000.0.
.IP -norm
Raw power divided by this normalizes the power,
.br
1 Double value.
.IP -fftnorm
Use local powers from '.fft' file to get 'norm'.
.IP infile
Input data file name (without a suffix) of floating point data.  A '.inf' file of the same name must also exist.
.\" cligPart OPTIONS end

.\" cligPart DESCRIPTION
.SH DESCRIPTION
This manual page was generated automagically by clig, the
Command Line Interface Generator. Actually the programmer
using clig was supposed to edit this part of the manual
page after
generating it with clig, but obviously (s)he didn't.

Sadly enough clig does not yet have the power to pick a good
program description out of blue air ;-(
.\" cligPart DESCRIPTION end
