FROM kernsuite/base:4
RUN docker-apt-install \
    python3-pip \
    python3-numpy \
    python3-future \
    python3-six \
    python3-scipy \
    python3-astropy \
    gfortran \
    pgplot5 \
    libx11-dev \
    libglib2.0-dev \
    libcfitsio-dev \
    libpng-dev \
    libfftw3-dev
ADD . /code
ENV PRESTO /code
ENV LD_LIBRARY_PATH /code/lib
WORKDIR /code/src
RUN make libpresto slalib
WORKDIR /code
RUN pip3 install /code
RUN python3 tests/test_presto_python.py
