[build-system]
build-backend = 'mesonpy'
requires = ['meson-python', 'numpy']

[project]
name = 'presto'
version = '5.0.3.dev7'
description = 'PulsaR Exploration and Search TOolkit'
requires-python = '>=3.9'
dependencies = ['numpy', 'scipy', 'astropy', 'matplotlib']
authors = [
  {name = '<PERSON>', email = '<EMAIL>'},
]

[project.urls]
"Source" = "https://github.com/scottransom/presto"
"ASCL" = "https://ascl.net/1107.017"

[tool.pixi.project]
channels = ["conda-forge"]
platforms = ["linux-64", "osx-arm64", "osx-64"]

[tool.pixi.pypi-dependencies]
presto = { path = ".", editable = true }

[tool.pixi.tasks]
