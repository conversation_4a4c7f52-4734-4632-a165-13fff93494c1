.\" clig manual page template
.\" (C) 1995 <PERSON> (<EMAIL>)
.\"
.\" This file was generated by
.\" clig -- command line interface generator
.\"
.\"
.\" Clig will always edit the lines between pairs of `cligPart ...',
.\" but will not complain, if a pair is missing. So, if you want to
.\" make up a certain part of the manual page by hand rather than have
.\" it edited by clig, remove the respective pair of cligPart-lines.
.\"
.\" cligPart TITLE
.TH "search_bin" 1 "12Mar10" "Clig-manuals" "Programmer's Manual"
.\" cligPart TITLE end

.\" cligPart NAME
.SH NAME
search_bin \- Searches a long FFT for binary pulsar candidates using a phase modulation search.
.\" cligPart NAME end

.\" cligPart SYNOPSIS
.SH SYNOPSIS
.B search_bin
[-ncand ncand]
[-minfft minfft]
[-maxfft maxfft]
[-flo flo]
[-fhi fhi]
[-rlo rlo]
[-rhi rhi]
[-lobin lobin]
[-overlap overlap]
[-harmsum harmsum]
[-numbetween numbetween]
[-stack stack]
[-interbin]
[-noalias]
infile
.\" cligPart SYNOPSIS end

.\" cligPart OPTIONS
.SH OPTIONS
.IP -ncand
Number of candidates to try to return,
.br
1 Int value between 1 and 10000.
.br
Default: `100'
.IP -minfft
Power-of-2 length of the shortest miniFFT,
.br
1 Int value between 8 and 1048576.
.br
Default: `32'
.IP -maxfft
Power-of-2 length of the longest miniFFT,
.br
1 Int value between 8 and 1048576.
.br
Default: `65536'
.IP -flo
The low frequency (Hz) to check,
.br
1 Float value between 0 and oo.
.br
Default: `1.0'
.IP -fhi
The high frequency (Hz) to check,
.br
1 Float value between 0 and oo.
.br
Default: `2000.0'
.IP -rlo
The low Fourier frequency to check,
.br
1 Int value between 0 and oo.
.IP -rhi
The high Fourier frequency to check,
.br
1 Int value between 0 and oo.
.IP -lobin
The first Fourier frequency in the data file,
.br
1 Int value between 0 and oo.
.br
Default: `0'
.IP -overlap
Fraction of a short FFT length to shift before performing another,
.br
1 Double value between 0.05 and 1.0.
.br
Default: `0.25'
.IP -harmsum
Number of harmonics to sum in the miniFFTs,
.br
1 Int value between 1 and 20.
.br
Default: `3'
.IP -numbetween
Number of points to interpolate per Fourier bin (2 gives the usual bin value and an interbin),
.br
1 Int value between 1 and 16.
.br
Default: `2'
.IP -stack
Number of stacked power spectra making up the data.  (The default means the data are complex amplitudes),
.br
1 Int value between 0 and oo.
.br
Default: `0'
.IP -interbin
Use interbinning instead of full-blown Fourier interpolation.  (Faster but less accurate and sensitive).
.IP -noalias
Do not add aliased powers to the harmonic sum.  (Faster but less accurate and sensitive).
.IP infile
Input file name (no suffix) of floating point fft data.  A '.inf' file of the same name must also exist.
.\" cligPart OPTIONS end

.SH DESCRIPTION
This program searches the Fourier transformed time series 
found in the input file for binary pulsars whose periodicities are
Doppler shifted to such an extent that their spread-out Fourier power
forms a set of frequency modulation sidebands.  The frequency of
modulation is simply the orbital frequency.  The routine outputs
formatted statistics for the best candidates found in the search
region.

Copyright 1999, Scott M. Ransom (<EMAIL>)
