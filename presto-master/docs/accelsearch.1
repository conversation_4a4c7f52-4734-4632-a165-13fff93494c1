.\" clig manual page template
.\" (C) 1995 <PERSON> (<EMAIL>)
.\"
.\" This file was generated by
.\" clig -- command line interface generator
.\"
.\"
.\" Clig will always edit the lines between pairs of `cligPart ...',
.\" but will not complain, if a pair is missing. So, if you want to
.\" make up a certain part of the manual page by hand rather than have
.\" it edited by clig, remove the respective pair of cligPart-lines.
.\"
.\" cligPart TITLE
.TH "accelsearch" 1 "09Jul20" "Clig-manuals" "Programmer's Manual"
.\" cligPart TITLE end

.\" cligPart NAME
.SH NAME
accelsearch \- Search an FFT or short time series for pulsars using a Fourier domain acceleration search with harmonic summing.
.\" cligPart NAME end

.\" cligPart SYNOPSIS
.SH SYNOPSIS
.B accelsearch
[-ncpus ncpus]
[-lobin lobin]
[-numharm numharm]
[-zmax zmax]
[-wmax wmax]
[-sigma sigma]
[-rlo rlo]
[-rhi rhi]
[-flo flo]
[-fhi fhi]
[-inmem]
[-photon]
[-median]
[-locpow]
[-zaplist zaplist]
[-baryv baryv]
[-otheropt]
[-noharmpolish]
[-noharmremove]
infile ...
.\" cligPart SYNOPSIS end

.\" cligPart OPTIONS
.SH OPTIONS
.IP -ncpus
Number of processors to use with OpenMP,
.br
1 Int value between 1 and oo.
.br
Default: `1'
.IP -lobin
The first Fourier frequency in the data file,
.br
1 Int value between 0 and oo.
.br
Default: `0'
.IP -numharm
The number of harmonics to sum (power-of-two),
.br
1 Int value between 1 and 32.
.br
Default: `8'
.IP -zmax
The max (+ and -) Fourier freq deriv to search,
.br
1 Int value between 0 and 1200.
.br
Default: `200'
.IP -wmax
The max (+ and -) Fourier freq double derivs to search,
.br
1 Int value between 0 and 4000.
.IP -sigma
Cutoff sigma for choosing candidates,
.br
1 Float value between 1.0 and 30.0.
.br
Default: `2.0'
.IP -rlo
The lowest Fourier frequency (of the highest harmonic!) to search,
.br
1 Double value between 0.0 and oo.
.IP -rhi
The highest Fourier frequency (of the highest harmonic!) to search,
.br
1 Double value between 0.0 and oo.
.IP -flo
The lowest frequency (Hz) (of the highest harmonic!) to search,
.br
1 Double value between 0.0 and oo.
.br
Default: `1.0'
.IP -fhi
The highest frequency (Hz) (of the highest harmonic!) to search,
.br
1 Double value between 0.0 and oo.
.br
Default: `10000.0'
.IP -inmem
Compute full f-fdot plane in memory.  Very fast, but only for short time series..
.IP -photon
Data is poissonian so use freq 0 as power normalization.
.IP -median
Use block-median power normalization (default).
.IP -locpow
Use double-tophat local-power normalization (not usually recommended).
.IP -zaplist
A file of freqs+widths to zap from the FFT (only if the input file is a *.[s]dat file),
.br
1 String value
.IP -baryv
The radial velocity component (v/c) towards the target during the obs,
.br
1 Double value between -0.1 and 0.1.
.br
Default: `0.0'
.IP -otheropt
Use the alternative optimization (for testing/debugging).
.IP -noharmpolish
Do not use 'harmpolish' by default.
.IP -noharmremove
Do not remove harmonically related candidates (never removed for numharm = 1).
.IP infile
Input file name(s) of the floating point .fft or .[s]dat file(s).  '.inf' file(s) of the same name must also exist.
.\" cligPart OPTIONS end

.\" cligPart DESCRIPTION
.SH DESCRIPTION
This manual page was generated automagically by clig, the
Command Line Interface Generator. Actually the programmer
using clig was supposed to edit this part of the manual
page after
generating it with clig, but obviously (s)he didn't.

Sadly enough clig does not yet have the power to pick a good
program description out of blue air ;-(
.\" cligPart DESCRIPTION end
