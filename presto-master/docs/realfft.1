.\" clig manual page template
.\" (C) 1995 <PERSON> (<EMAIL>)
.\"
.\" This file was generated by
.\" clig -- command line interface generator
.\"
.\"
.\" Clig will always edit the lines between pairs of `cligPart ...',
.\" but will not complain, if a pair is missing. So, if you want to
.\" make up a certain part of the manual page by hand rather than have
.\" it edited by clig, remove the respective pair of cligPart-lines.
.\"
.\" cligPart TITLE
.TH "realfft" 1 "09Jul20" "Clig-manuals" "Programmer's Manual"
.\" cligPart TITLE end

.\" cligPart NAME
.SH NAME
realfft \- Perform a single-precision FFT of real data or its inverse
.\" cligPart NAME end

.\" cligPart SYNOPSIS
.SH SYNOPSIS
.B realfft
[-fwd]
[-inv]
[-del]
[-disk]
[-mem]
[-tmpdir tmpdir]
[-outdir outdir]
infiles ...
.\" cligPart SYNOPSIS end

.\" cligPart OPTIONS
.SH OPTIONS
.IP -fwd
Force an forward FFT (sign=-1) to be performed.
.IP -inv
Force an inverse FFT (sign=+1) to be performed.
.IP -del
Delete the original file(s) when performing the FFT.
.IP -disk
Force the use of the out-of-core memory FFT.
.IP -mem
Force the use of the in-core memory FFT.
.IP -tmpdir
Scratch directory for temp file(s) in out-of-core FFT,
.br
1 String value
.IP -outdir
Directory where result file(s) will reside,
.br
1 String value
.IP infiles
Input data file(s).
.\" cligPart OPTIONS end

.\" cligPart DESCRIPTION
.SH DESCRIPTION
This manual page was generated automagically by clig, the
Command Line Interface Generator. Actually the programmer
using clig was supposed to edit this part of the manual
page after
generating it with clig, but obviously (s)he didn't.

Sadly enough clig does not yet have the power to pick a good
program description out of blue air ;-(
.\" cligPart DESCRIPTION end
